#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32744 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=18796, tid=21048
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.8+9 (21.0.8+9) (build 21.0.8+9-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.8+9 (21.0.8+9-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java\ss_ws --pipe=\\.\pipe\lsp-61e1700b38113ac45772b3ad35a4d435-sock

Host: 11th Gen Intel(R) Core(TM) i5-11400H @ 2.70GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Thu Sep 25 03:08:50 2025 SE Asia Standard Time elapsed time: 5.955382 seconds (0d 0h 0m 5s)

---------------  T H R E A D  ---------------

Current thread (0x000002c268d64a30):  JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=21048, stack(0x000000e212c00000,0x000000e212d00000) (1024K)]


Current CompileTask:
C2:5955 5634       4       org.eclipse.jdt.internal.compiler.parser.Parser::consumeRule (7748 bytes)

Stack: [0x000000e212c00000,0x000000e212d00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d2449]
V  [jvm.dll+0x8ae341]
V  [jvm.dll+0x8b08be]
V  [jvm.dll+0x8b0fa3]
V  [jvm.dll+0x280c96]
V  [jvm.dll+0xc581d]
V  [jvm.dll+0xc5d53]
V  [jvm.dll+0x6fdd5c]
V  [jvm.dll+0x1e14d6]
V  [jvm.dll+0x249022]
V  [jvm.dll+0x2484af]
V  [jvm.dll+0x1c89ee]
V  [jvm.dll+0x257d4d]
V  [jvm.dll+0x2562ea]
V  [jvm.dll+0x3f2d16]
V  [jvm.dll+0x857e6b]
V  [jvm.dll+0x6d0b0d]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002c260e95fe0, length=42, elements={
0x000002c207c548f0, 0x000002c21e6ee110, 0x000002c207cef0f0, 0x000002c207cf2ad0,
0x000002c207cf7b50, 0x000002c207cfa720, 0x000002c207cfc3a0, 0x000002c207d056c0,
0x000002c207d08260, 0x000002c260b7b0e0, 0x000002c260d1fb00, 0x000002c26644a860,
0x000002c266975030, 0x000002c2666989d0, 0x000002c266ba98d0, 0x000002c266baa700,
0x000002c2668dcc10, 0x000002c266d38470, 0x000002c266cd2a10, 0x000002c266ce8bc0,
0x000002c266ce98e0, 0x000002c266ce9f70, 0x000002c266cea600, 0x000002c266ceb320,
0x000002c266ce9250, 0x000002c266ce7ea0, 0x000002c266ceac90, 0x000002c266ce8530,
0x000002c268a6c7f0, 0x000002c268a6c160, 0x000002c268a6e8c0, 0x000002c268a6d510,
0x000002c268a6ce80, 0x000002c268a6ef50, 0x000002c268a6f5e0, 0x000002c268a723d0,
0x000002c268a716b0, 0x000002c268a6dba0, 0x000002c268a6fc70, 0x000002c268a72a60,
0x000002c268d66570, 0x000002c268d64a30
}

Java Threads: ( => current thread )
  0x000002c207c548f0 JavaThread "main"                              [_thread_blocked, id=5732, stack(0x000000e211d00000,0x000000e211e00000) (1024K)]
  0x000002c21e6ee110 JavaThread "Reference Handler"          daemon [_thread_blocked, id=18464, stack(0x000000e212100000,0x000000e212200000) (1024K)]
  0x000002c207cef0f0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=12440, stack(0x000000e212200000,0x000000e212300000) (1024K)]
  0x000002c207cf2ad0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=15096, stack(0x000000e212300000,0x000000e212400000) (1024K)]
  0x000002c207cf7b50 JavaThread "Attach Listener"            daemon [_thread_blocked, id=23360, stack(0x000000e212400000,0x000000e212500000) (1024K)]
  0x000002c207cfa720 JavaThread "Service Thread"             daemon [_thread_blocked, id=22812, stack(0x000000e212500000,0x000000e212600000) (1024K)]
  0x000002c207cfc3a0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=19364, stack(0x000000e212600000,0x000000e212700000) (1024K)]
  0x000002c207d056c0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=22212, stack(0x000000e212700000,0x000000e212800000) (1024K)]
  0x000002c207d08260 JavaThread "C1 CompilerThread0"         daemon [_thread_in_native, id=12380, stack(0x000000e212800000,0x000000e212900000) (1024K)]
  0x000002c260b7b0e0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=18276, stack(0x000000e212900000,0x000000e212a00000) (1024K)]
  0x000002c260d1fb00 JavaThread "Notification Thread"        daemon [_thread_blocked, id=8596, stack(0x000000e212b00000,0x000000e212c00000) (1024K)]
  0x000002c26644a860 JavaThread "Active Thread: Equinox Container: 4b1a6300-8e3f-4149-9f0f-5b1b6a36a8a7"        [_thread_blocked, id=22388, stack(0x000000e213300000,0x000000e213400000) (1024K)]
  0x000002c266975030 JavaThread "Framework Event Dispatcher: Equinox Container: 4b1a6300-8e3f-4149-9f0f-5b1b6a36a8a7" daemon [_thread_blocked, id=14364, stack(0x000000e213400000,0x000000e213500000) (1024K)]
  0x000002c2666989d0 JavaThread "Start Level: Equinox Container: 4b1a6300-8e3f-4149-9f0f-5b1b6a36a8a7" daemon [_thread_blocked, id=21868, stack(0x000000e213500000,0x000000e213600000) (1024K)]
  0x000002c266ba98d0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=9940, stack(0x000000e213700000,0x000000e213800000) (1024K)]
  0x000002c266baa700 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=10716, stack(0x000000e213800000,0x000000e213900000) (1024K)]
  0x000002c2668dcc10 JavaThread "Worker-JM"                         [_thread_blocked, id=15720, stack(0x000000e213900000,0x000000e213a00000) (1024K)]
  0x000002c266d38470 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=16080, stack(0x000000e213c00000,0x000000e213d00000) (1024K)]
  0x000002c266cd2a10 JavaThread "Worker-0: Publish Diagnostics"        [_thread_in_vm, id=23540, stack(0x000000e213d00000,0x000000e213e00000) (1024K)]
  0x000002c266ce8bc0 JavaThread "Worker-1"                          [_thread_blocked, id=21600, stack(0x000000e213e00000,0x000000e213f00000) (1024K)]
  0x000002c266ce98e0 JavaThread "Thread-2"                   daemon [_thread_in_native, id=3468, stack(0x000000e213f00000,0x000000e214000000) (1024K)]
  0x000002c266ce9f70 JavaThread "Thread-3"                   daemon [_thread_in_native, id=21984, stack(0x000000e214000000,0x000000e214100000) (1024K)]
  0x000002c266cea600 JavaThread "Thread-4"                   daemon [_thread_in_native, id=17272, stack(0x000000e214100000,0x000000e214200000) (1024K)]
  0x000002c266ceb320 JavaThread "Thread-5"                   daemon [_thread_in_native, id=18216, stack(0x000000e214200000,0x000000e214300000) (1024K)]
  0x000002c266ce9250 JavaThread "Thread-6"                   daemon [_thread_in_native, id=22164, stack(0x000000e214300000,0x000000e214400000) (1024K)]
  0x000002c266ce7ea0 JavaThread "Thread-7"                   daemon [_thread_in_native, id=20540, stack(0x000000e214400000,0x000000e214500000) (1024K)]
  0x000002c266ceac90 JavaThread "Thread-8"                   daemon [_thread_in_native, id=23004, stack(0x000000e214500000,0x000000e214600000) (1024K)]
  0x000002c266ce8530 JavaThread "Thread-9"                   daemon [_thread_in_native, id=19216, stack(0x000000e214600000,0x000000e214700000) (1024K)]
  0x000002c268a6c7f0 JavaThread "Thread-10"                  daemon [_thread_in_native, id=18880, stack(0x000000e214700000,0x000000e214800000) (1024K)]
  0x000002c268a6c160 JavaThread "Thread-11"                  daemon [_thread_in_native, id=15864, stack(0x000000e214800000,0x000000e214900000) (1024K)]
  0x000002c268a6e8c0 JavaThread "Thread-12"                  daemon [_thread_in_native, id=5268, stack(0x000000e214900000,0x000000e214a00000) (1024K)]
  0x000002c268a6d510 JavaThread "Thread-13"                  daemon [_thread_in_native, id=1120, stack(0x000000e214a00000,0x000000e214b00000) (1024K)]
  0x000002c268a6ce80 JavaThread "Thread-14"                  daemon [_thread_in_native, id=6612, stack(0x000000e214b00000,0x000000e214c00000) (1024K)]
  0x000002c268a6ef50 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=16696, stack(0x000000e214c00000,0x000000e214d00000) (1024K)]
  0x000002c268a6f5e0 JavaThread "Worker-2"                          [_thread_blocked, id=7044, stack(0x000000e214d00000,0x000000e214e00000) (1024K)]
  0x000002c268a723d0 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=16556, stack(0x000000e214e00000,0x000000e214f00000) (1024K)]
  0x000002c268a716b0 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=9404, stack(0x000000e214f00000,0x000000e215000000) (1024K)]
  0x000002c268a6dba0 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=19388, stack(0x000000e215000000,0x000000e215100000) (1024K)]
  0x000002c268a6fc70 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=18488, stack(0x000000e215100000,0x000000e215200000) (1024K)]
  0x000002c268a72a60 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_in_vm, id=2484, stack(0x000000e215200000,0x000000e215300000) (1024K)]
  0x000002c268d66570 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=14668, stack(0x000000e212a00000,0x000000e212b00000) (1024K)]
=>0x000002c268d64a30 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=21048, stack(0x000000e212c00000,0x000000e212d00000) (1024K)]
Total: 42

Other Threads:
  0x000002c205a7e950 VMThread "VM Thread"                           [id=10928, stack(0x000000e212000000,0x000000e212100000) (1024K)]
  0x000002c207cbfa50 WatcherThread "VM Periodic Task Thread"        [id=20872, stack(0x000000e211f00000,0x000000e212000000) (1024K)]
  0x000002c207c73250 WorkerThread "GC Thread#0"                     [id=20232, stack(0x000000e211e00000,0x000000e211f00000) (1024K)]
  0x000002c2664185e0 WorkerThread "GC Thread#1"                     [id=2860, stack(0x000000e212d00000,0x000000e212e00000) (1024K)]
  0x000002c2665f9010 WorkerThread "GC Thread#2"                     [id=10480, stack(0x000000e212e00000,0x000000e212f00000) (1024K)]
  0x000002c26619bdb0 WorkerThread "GC Thread#3"                     [id=13932, stack(0x000000e212f00000,0x000000e213000000) (1024K)]
  0x000002c26619c150 WorkerThread "GC Thread#4"                     [id=15284, stack(0x000000e213000000,0x000000e213100000) (1024K)]
  0x000002c26619c4f0 WorkerThread "GC Thread#5"                     [id=6024, stack(0x000000e213100000,0x000000e213200000) (1024K)]
  0x000002c2664688d0 WorkerThread "GC Thread#6"                     [id=19436, stack(0x000000e213200000,0x000000e213300000) (1024K)]
  0x000002c266b52c30 WorkerThread "GC Thread#7"                     [id=16840, stack(0x000000e213600000,0x000000e213700000) (1024K)]
  0x000002c266bcc7a0 WorkerThread "GC Thread#8"                     [id=6608, stack(0x000000e213a00000,0x000000e213b00000) (1024K)]
  0x000002c266bd5e60 WorkerThread "GC Thread#9"                     [id=22328, stack(0x000000e213b00000,0x000000e213c00000) (1024K)]
Total: 12

Threads with active compile tasks:
C2 CompilerThread0  5968 5302       4       java.lang.reflect.Field::get (39 bytes)
C1 CompilerThread0  5968 5802       3       java.util.regex.Pattern::compile (537 bytes)
C2 CompilerThread1  5968 5626       4       lombok.core.AST::buildWithField (21 bytes)
C2 CompilerThread2  5968 5634       4       org.eclipse.jdt.internal.compiler.parser.Parser::consumeRule (7748 bytes)
Total: 4

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffaeb923458] Metaspace_lock - owner thread: 0x000002c266cd2a10

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002c21f000000-0x000002c21fba0000-0x000002c21fba0000), size 12189696, SharedBaseAddress: 0x000002c21f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002c220000000-0x000002c260000000, reserved size: 1073741824
Narrow klass base: 0x000002c21f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 12 total, 12 available
 Memory: 16163M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 10

Heap:
 PSYoungGen      total 23552K, used 20961K [0x00000000d5580000, 0x00000000d6f00000, 0x0000000100000000)
  eden space 21504K, 89% used [0x00000000d5580000,0x00000000d683e550,0x00000000d6a80000)
  from space 2048K, 86% used [0x00000000d6d00000,0x00000000d6eba020,0x00000000d6f00000)
  to   space 2048K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6d00000)
 ParOldGen       total 68608K, used 31891K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 46% used [0x0000000080000000,0x0000000081f24d60,0x0000000084300000)
 Metaspace       used 41114K, committed 42112K, reserved 1114112K
  class space    used 4119K, committed 4608K, reserved 1048576K

Card table byte_map: [0x000002c207610000,0x000002c207a20000] _byte_map_base: 0x000002c207210000

Marking Bits: (ParMarkBitMap*) 0x00007ffaeb92a340
 Begin Bits: [0x000002c21a0f0000, 0x000002c21c0f0000)
 End Bits:   [0x000002c21c0f0000, 0x000002c21e0f0000)

Polling page: 0x000002c205ad0000

Metaspace:

Usage:
  Non-class:     36.13 MB used.
      Class:      4.02 MB used.
       Both:     40.15 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      36.62 MB ( 57%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       4.50 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      41.12 MB (  4%) committed. 

Chunk freelists:
   Non-Class:  10.81 MB
       Class:  11.54 MB
        Both:  22.35 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 58.38 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 806.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 658.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 20.
num_chunks_taken_from_freelist: 2518.
num_chunk_merges: 11.
num_chunk_splits: 1612.
num_chunks_enlarged: 1023.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=3353Kb max_used=3353Kb free=116646Kb
 bounds [0x000002c212910000, 0x000002c212c60000, 0x000002c219e40000]
CodeHeap 'profiled nmethods': size=120000Kb used=11762Kb max_used=11762Kb free=108237Kb
 bounds [0x000002c20ae40000, 0x000002c20b9c0000, 0x000002c212370000]
CodeHeap 'non-nmethods': size=5760Kb used=1409Kb max_used=1470Kb free=4351Kb
 bounds [0x000002c212370000, 0x000002c2125e0000, 0x000002c212910000]
CodeCache: size=245760Kb, used=16524Kb, max_used=16585Kb, free=229234Kb
 total_blobs=6075, nmethods=5366, adapters=615, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 5.949 Thread 0x000002c207d08260 nmethod 5752 0x000002c20b9b4b90 code [0x000002c20b9b4e20, 0x000002c20b9b5ba0]
Event: 5.949 Thread 0x000002c207d08260 5766       1       org.eclipse.jdt.internal.compiler.classfmt.FieldInfo::getTypeAnnotations (2 bytes)
Event: 5.949 Thread 0x000002c207d08260 nmethod 5766 0x000002c212c55910 code [0x000002c212c55aa0, 0x000002c212c55b70]
Event: 5.949 Thread 0x000002c207d08260 5776       1       org.eclipse.jdt.internal.compiler.lookup.TypeBinding::getTypeAnnotations (5 bytes)
Event: 5.949 Thread 0x000002c207d08260 nmethod 5776 0x000002c212c55c10 code [0x000002c212c55da0, 0x000002c212c55e68]
Event: 5.949 Thread 0x000002c207d08260 5780       1       org.eclipse.jdt.internal.compiler.lookup.TypeVariableBinding::kind (4 bytes)
Event: 5.949 Thread 0x000002c207d08260 nmethod 5780 0x000002c212c55f10 code [0x000002c212c560a0, 0x000002c212c56168]
Event: 5.950 Thread 0x000002c207d08260 5786       3       java.lang.Boolean::parseBoolean (7 bytes)
Event: 5.950 Thread 0x000002c207d08260 nmethod 5786 0x000002c20b9b6010 code [0x000002c20b9b61c0, 0x000002c20b9b62f0]
Event: 5.951 Thread 0x000002c207d08260 5787       3       org.eclipse.jdt.internal.compiler.ASTVisitor::<init> (5 bytes)
Event: 5.951 Thread 0x000002c207d08260 nmethod 5787 0x000002c20b9b6390 code [0x000002c20b9b6540, 0x000002c20b9b66a8]
Event: 5.951 Thread 0x000002c207d08260 5788       3       org.eclipse.jdt.internal.core.util.ReferenceInfoAdapter::acceptAnnotationTypeReference (1 bytes)
Event: 5.951 Thread 0x000002c207d08260 nmethod 5788 0x000002c20b9b6790 code [0x000002c20b9b6920, 0x000002c20b9b6a28]
Event: 5.952 Thread 0x000002c207d08260 5792       3       org.eclipse.jdt.internal.compiler.parser.ScannerHelper::isJavaIdentifierPart0 (156 bytes)
Event: 5.952 Thread 0x000002c207d08260 nmethod 5792 0x000002c20b9b6b10 code [0x000002c20b9b6d80, 0x000002c20b9b7400]
Event: 5.952 Thread 0x000002c207d08260 5793   !   3       org.eclipse.jdt.internal.compiler.parser.ScannerHelper::isBitSet (27 bytes)
Event: 5.952 Thread 0x000002c207d08260 nmethod 5793 0x000002c20b9b7890 code [0x000002c20b9b7a40, 0x000002c20b9b7c88]
Event: 5.952 Thread 0x000002c207d08260 5789       3       org.eclipse.jdt.internal.compiler.parser.ScannerHelper::isJavaIdentifierPart (143 bytes)
Event: 5.952 Thread 0x000002c207d08260 nmethod 5789 0x000002c20b9b7e90 code [0x000002c20b9b81a0, 0x000002c20b9b8dd0]
Event: 5.952 Thread 0x000002c207d08260 5791       1       org.eclipse.jdt.internal.compiler.ast.LocalDeclaration::isReceiver (2 bytes)

GC Heap History (20 events):
Event: 3.743 GC heap before
{Heap before GC invocations=15 (full 1):
 PSYoungGen      total 24064K, used 23780K [0x00000000d5580000, 0x00000000d7180000, 0x0000000100000000)
  eden space 21504K, 100% used [0x00000000d5580000,0x00000000d6a80000,0x00000000d6a80000)
  from space 2560K, 88% used [0x00000000d6a80000,0x00000000d6cb9000,0x00000000d6d00000)
  to   space 3584K, 0% used [0x00000000d6e00000,0x00000000d6e00000,0x00000000d7180000)
 ParOldGen       total 68608K, used 22525K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 32% used [0x0000000080000000,0x00000000815ff7d0,0x0000000084300000)
 Metaspace       used 31945K, committed 32832K, reserved 1114112K
  class space    used 3221K, committed 3648K, reserved 1048576K
}
Event: 3.748 GC heap after
{Heap after GC invocations=15 (full 1):
 PSYoungGen      total 24064K, used 2368K [0x00000000d5580000, 0x00000000d7080000, 0x0000000100000000)
  eden space 21504K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a80000)
  from space 2560K, 92% used [0x00000000d6e00000,0x00000000d7050010,0x00000000d7080000)
  to   space 2560K, 0% used [0x00000000d6a80000,0x00000000d6a80000,0x00000000d6d00000)
 ParOldGen       total 68608K, used 24606K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 35% used [0x0000000080000000,0x00000000818078d0,0x0000000084300000)
 Metaspace       used 31945K, committed 32832K, reserved 1114112K
  class space    used 3221K, committed 3648K, reserved 1048576K
}
Event: 3.813 GC heap before
{Heap before GC invocations=16 (full 1):
 PSYoungGen      total 24064K, used 23872K [0x00000000d5580000, 0x00000000d7080000, 0x0000000100000000)
  eden space 21504K, 100% used [0x00000000d5580000,0x00000000d6a80000,0x00000000d6a80000)
  from space 2560K, 92% used [0x00000000d6e00000,0x00000000d7050010,0x00000000d7080000)
  to   space 2560K, 0% used [0x00000000d6a80000,0x00000000d6a80000,0x00000000d6d00000)
 ParOldGen       total 68608K, used 24606K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 35% used [0x0000000080000000,0x00000000818078d0,0x0000000084300000)
 Metaspace       used 32059K, committed 33024K, reserved 1114112K
  class space    used 3224K, committed 3648K, reserved 1048576K
}
Event: 3.817 GC heap after
{Heap after GC invocations=16 (full 1):
 PSYoungGen      total 24064K, used 2104K [0x00000000d5580000, 0x00000000d6f80000, 0x0000000100000000)
  eden space 21504K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a80000)
  from space 2560K, 82% used [0x00000000d6a80000,0x00000000d6c8e200,0x00000000d6d00000)
  to   space 2560K, 0% used [0x00000000d6d00000,0x00000000d6d00000,0x00000000d6f80000)
 ParOldGen       total 68608K, used 26862K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 39% used [0x0000000080000000,0x0000000081a3b8e0,0x0000000084300000)
 Metaspace       used 32059K, committed 33024K, reserved 1114112K
  class space    used 3224K, committed 3648K, reserved 1048576K
}
Event: 4.111 GC heap before
{Heap before GC invocations=17 (full 1):
 PSYoungGen      total 24064K, used 23608K [0x00000000d5580000, 0x00000000d6f80000, 0x0000000100000000)
  eden space 21504K, 100% used [0x00000000d5580000,0x00000000d6a80000,0x00000000d6a80000)
  from space 2560K, 82% used [0x00000000d6a80000,0x00000000d6c8e200,0x00000000d6d00000)
  to   space 2560K, 0% used [0x00000000d6d00000,0x00000000d6d00000,0x00000000d6f80000)
 ParOldGen       total 68608K, used 26862K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 39% used [0x0000000080000000,0x0000000081a3b8e0,0x0000000084300000)
 Metaspace       used 33921K, committed 34880K, reserved 1114112K
  class space    used 3462K, committed 3904K, reserved 1048576K
}
Event: 4.114 GC heap after
{Heap after GC invocations=17 (full 1):
 PSYoungGen      total 23040K, used 1364K [0x00000000d5580000, 0x00000000d6e80000, 0x0000000100000000)
  eden space 21504K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a80000)
  from space 1536K, 88% used [0x00000000d6d00000,0x00000000d6e553c8,0x00000000d6e80000)
  to   space 2048K, 0% used [0x00000000d6a80000,0x00000000d6a80000,0x00000000d6c80000)
 ParOldGen       total 68608K, used 28838K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 42% used [0x0000000080000000,0x0000000081c29ae0,0x0000000084300000)
 Metaspace       used 33921K, committed 34880K, reserved 1114112K
  class space    used 3462K, committed 3904K, reserved 1048576K
}
Event: 4.345 GC heap before
{Heap before GC invocations=18 (full 1):
 PSYoungGen      total 23040K, used 22868K [0x00000000d5580000, 0x00000000d6e80000, 0x0000000100000000)
  eden space 21504K, 100% used [0x00000000d5580000,0x00000000d6a80000,0x00000000d6a80000)
  from space 1536K, 88% used [0x00000000d6d00000,0x00000000d6e553c8,0x00000000d6e80000)
  to   space 2048K, 0% used [0x00000000d6a80000,0x00000000d6a80000,0x00000000d6c80000)
 ParOldGen       total 68608K, used 28838K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 42% used [0x0000000080000000,0x0000000081c29ae0,0x0000000084300000)
 Metaspace       used 34923K, committed 35840K, reserved 1114112K
  class space    used 3587K, committed 4032K, reserved 1048576K
}
Event: 4.347 GC heap after
{Heap after GC invocations=18 (full 1):
 PSYoungGen      total 23552K, used 384K [0x00000000d5580000, 0x00000000d6d80000, 0x0000000100000000)
  eden space 21504K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a80000)
  from space 2048K, 18% used [0x00000000d6a80000,0x00000000d6ae0000,0x00000000d6c80000)
  to   space 1024K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 30046K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 43% used [0x0000000080000000,0x0000000081d57968,0x0000000084300000)
 Metaspace       used 34923K, committed 35840K, reserved 1114112K
  class space    used 3587K, committed 4032K, reserved 1048576K
}
Event: 4.356 GC heap before
{Heap before GC invocations=19 (full 1):
 PSYoungGen      total 23552K, used 889K [0x00000000d5580000, 0x00000000d6d80000, 0x0000000100000000)
  eden space 21504K, 2% used [0x00000000d5580000,0x00000000d55fe5b0,0x00000000d6a80000)
  from space 2048K, 18% used [0x00000000d6a80000,0x00000000d6ae0000,0x00000000d6c80000)
  to   space 1024K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 30046K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 43% used [0x0000000080000000,0x0000000081d57968,0x0000000084300000)
 Metaspace       used 34965K, committed 35840K, reserved 1114112K
  class space    used 3592K, committed 4032K, reserved 1048576K
}
Event: 4.357 GC heap after
{Heap after GC invocations=19 (full 1):
 PSYoungGen      total 22016K, used 352K [0x00000000d5580000, 0x00000000d6d00000, 0x0000000100000000)
  eden space 21504K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a80000)
  from space 512K, 68% used [0x00000000d6c80000,0x00000000d6cd8010,0x00000000d6d00000)
  to   space 512K, 0% used [0x00000000d6c00000,0x00000000d6c00000,0x00000000d6c80000)
 ParOldGen       total 68608K, used 30342K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 44% used [0x0000000080000000,0x0000000081da1968,0x0000000084300000)
 Metaspace       used 34965K, committed 35840K, reserved 1114112K
  class space    used 3592K, committed 4032K, reserved 1048576K
}
Event: 4.357 GC heap before
{Heap before GC invocations=20 (full 2):
 PSYoungGen      total 22016K, used 352K [0x00000000d5580000, 0x00000000d6d00000, 0x0000000100000000)
  eden space 21504K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a80000)
  from space 512K, 68% used [0x00000000d6c80000,0x00000000d6cd8010,0x00000000d6d00000)
  to   space 512K, 0% used [0x00000000d6c00000,0x00000000d6c00000,0x00000000d6c80000)
 ParOldGen       total 68608K, used 30342K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 44% used [0x0000000080000000,0x0000000081da1968,0x0000000084300000)
 Metaspace       used 34965K, committed 35840K, reserved 1114112K
  class space    used 3592K, committed 4032K, reserved 1048576K
}
Event: 4.397 GC heap after
{Heap after GC invocations=20 (full 2):
 PSYoungGen      total 22016K, used 0K [0x00000000d5580000, 0x00000000d6d00000, 0x0000000100000000)
  eden space 21504K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a80000)
  from space 512K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d6d00000)
  to   space 512K, 0% used [0x00000000d6c00000,0x00000000d6c00000,0x00000000d6c80000)
 ParOldGen       total 68608K, used 29232K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 42% used [0x0000000080000000,0x0000000081c8c188,0x0000000084300000)
 Metaspace       used 34965K, committed 35840K, reserved 1114112K
  class space    used 3592K, committed 4032K, reserved 1048576K
}
Event: 4.551 GC heap before
{Heap before GC invocations=21 (full 2):
 PSYoungGen      total 22016K, used 21504K [0x00000000d5580000, 0x00000000d6d00000, 0x0000000100000000)
  eden space 21504K, 100% used [0x00000000d5580000,0x00000000d6a80000,0x00000000d6a80000)
  from space 512K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d6d00000)
  to   space 512K, 0% used [0x00000000d6c00000,0x00000000d6c00000,0x00000000d6c80000)
 ParOldGen       total 68608K, used 29232K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 42% used [0x0000000080000000,0x0000000081c8c188,0x0000000084300000)
 Metaspace       used 35743K, committed 36672K, reserved 1114112K
  class space    used 3652K, committed 4096K, reserved 1048576K
}
Event: 4.553 GC heap after
{Heap after GC invocations=21 (full 2):
 PSYoungGen      total 22016K, used 494K [0x00000000d5580000, 0x00000000d6d80000, 0x0000000100000000)
  eden space 21504K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a80000)
  from space 512K, 96% used [0x00000000d6c00000,0x00000000d6c7bb60,0x00000000d6c80000)
  to   space 1024K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 29818K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 43% used [0x0000000080000000,0x0000000081d1e880,0x0000000084300000)
 Metaspace       used 35743K, committed 36672K, reserved 1114112K
  class space    used 3652K, committed 4096K, reserved 1048576K
}
Event: 4.605 GC heap before
{Heap before GC invocations=22 (full 2):
 PSYoungGen      total 22016K, used 21998K [0x00000000d5580000, 0x00000000d6d80000, 0x0000000100000000)
  eden space 21504K, 100% used [0x00000000d5580000,0x00000000d6a80000,0x00000000d6a80000)
  from space 512K, 96% used [0x00000000d6c00000,0x00000000d6c7bb60,0x00000000d6c80000)
  to   space 1024K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 29818K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 43% used [0x0000000080000000,0x0000000081d1e880,0x0000000084300000)
 Metaspace       used 36089K, committed 36992K, reserved 1114112K
  class space    used 3674K, committed 4096K, reserved 1048576K
}
Event: 4.606 GC heap after
{Heap after GC invocations=22 (full 2):
 PSYoungGen      total 22016K, used 480K [0x00000000d5580000, 0x00000000d6d00000, 0x0000000100000000)
  eden space 21504K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a80000)
  from space 512K, 93% used [0x00000000d6c80000,0x00000000d6cf8000,0x00000000d6d00000)
  to   space 1024K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6c00000)
 ParOldGen       total 68608K, used 30050K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 43% used [0x0000000080000000,0x0000000081d58880,0x0000000084300000)
 Metaspace       used 36089K, committed 36992K, reserved 1114112K
  class space    used 3674K, committed 4096K, reserved 1048576K
}
Event: 5.457 GC heap before
{Heap before GC invocations=23 (full 2):
 PSYoungGen      total 22016K, used 21984K [0x00000000d5580000, 0x00000000d6d00000, 0x0000000100000000)
  eden space 21504K, 100% used [0x00000000d5580000,0x00000000d6a80000,0x00000000d6a80000)
  from space 512K, 93% used [0x00000000d6c80000,0x00000000d6cf8000,0x00000000d6d00000)
  to   space 1024K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6c00000)
 ParOldGen       total 68608K, used 30050K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 43% used [0x0000000080000000,0x0000000081d58880,0x0000000084300000)
 Metaspace       used 39694K, committed 40640K, reserved 1114112K
  class space    used 3993K, committed 4416K, reserved 1048576K
}
Event: 5.459 GC heap after
{Heap after GC invocations=23 (full 2):
 PSYoungGen      total 22528K, used 1014K [0x00000000d5580000, 0x00000000d6f80000, 0x0000000100000000)
  eden space 21504K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a80000)
  from space 1024K, 99% used [0x00000000d6b00000,0x00000000d6bfda90,0x00000000d6c00000)
  to   space 2560K, 0% used [0x00000000d6d00000,0x00000000d6d00000,0x00000000d6f80000)
 ParOldGen       total 68608K, used 30922K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 45% used [0x0000000080000000,0x0000000081e328b0,0x0000000084300000)
 Metaspace       used 39694K, committed 40640K, reserved 1114112K
  class space    used 3993K, committed 4416K, reserved 1048576K
}
Event: 5.828 GC heap before
{Heap before GC invocations=24 (full 2):
 PSYoungGen      total 22528K, used 22518K [0x00000000d5580000, 0x00000000d6f80000, 0x0000000100000000)
  eden space 21504K, 100% used [0x00000000d5580000,0x00000000d6a80000,0x00000000d6a80000)
  from space 1024K, 99% used [0x00000000d6b00000,0x00000000d6bfda90,0x00000000d6c00000)
  to   space 2560K, 0% used [0x00000000d6d00000,0x00000000d6d00000,0x00000000d6f80000)
 ParOldGen       total 68608K, used 30922K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 45% used [0x0000000080000000,0x0000000081e328b0,0x0000000084300000)
 Metaspace       used 40702K, committed 41728K, reserved 1114112K
  class space    used 4095K, committed 4544K, reserved 1048576K
}
Event: 5.831 GC heap after
{Heap after GC invocations=24 (full 2):
 PSYoungGen      total 23552K, used 1768K [0x00000000d5580000, 0x00000000d6f00000, 0x0000000100000000)
  eden space 21504K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a80000)
  from space 2048K, 86% used [0x00000000d6d00000,0x00000000d6eba020,0x00000000d6f00000)
  to   space 2048K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6d00000)
 ParOldGen       total 68608K, used 31891K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 46% used [0x0000000080000000,0x0000000081f24d60,0x0000000084300000)
 Metaspace       used 40702K, committed 41728K, reserved 1114112K
  class space    used 4095K, committed 4544K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.008 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
Event: 0.074 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.091 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
Event: 0.096 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
Event: 0.098 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
Event: 0.100 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.114 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
Event: 0.162 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
Event: 1.051 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
Event: 1.894 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-71916\jna16087846074989327254.dll

Deoptimization events (20 events):
Event: 5.904 Thread 0x000002c266cd2a10 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002c2129b3510 relative=0x00000000000002b0
Event: 5.904 Thread 0x000002c266cd2a10 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002c2129b3510 method=java.util.HashMap.getNode(Ljava/lang/Object;)Ljava/util/HashMap$Node; @ 85 c2
Event: 5.904 Thread 0x000002c266cd2a10 DEOPT PACKING pc=0x000002c2129b3510 sp=0x000000e213dfe5a0
Event: 5.904 Thread 0x000002c266cd2a10 DEOPT UNPACKING pc=0x000002c2123c6da2 sp=0x000000e213dfe4a0 mode 2
Event: 5.907 Thread 0x000002c266cd2a10 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002c212ad10c4 relative=0x0000000000000284
Event: 5.907 Thread 0x000002c266cd2a10 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002c212ad10c4 method=org.eclipse.core.internal.watson.ElementTree.getElementData(Lorg/eclipse/core/runtime/IPath;)Ljava/lang/Object; @ 29 c2
Event: 5.907 Thread 0x000002c266cd2a10 DEOPT PACKING pc=0x000002c212ad10c4 sp=0x000000e213dfe850
Event: 5.907 Thread 0x000002c266cd2a10 DEOPT UNPACKING pc=0x000002c2123c6da2 sp=0x000000e213dfe7f8 mode 2
Event: 5.907 Thread 0x000002c268a72a60 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002c212ad10c4 relative=0x0000000000000284
Event: 5.907 Thread 0x000002c268a72a60 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002c212ad10c4 method=org.eclipse.core.internal.watson.ElementTree.getElementData(Lorg/eclipse/core/runtime/IPath;)Ljava/lang/Object; @ 29 c2
Event: 5.907 Thread 0x000002c268a72a60 DEOPT PACKING pc=0x000002c212ad10c4 sp=0x000000e2152fc270
Event: 5.907 Thread 0x000002c268a72a60 DEOPT UNPACKING pc=0x000002c2123c6da2 sp=0x000000e2152fc218 mode 2
Event: 5.907 Thread 0x000002c266cd2a10 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002c2129b0c18 relative=0x00000000000002b8
Event: 5.907 Thread 0x000002c266cd2a10 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002c2129b0c18 method=java.util.HashMap.getNode(Ljava/lang/Object;)Ljava/util/HashMap$Node; @ 85 c2
Event: 5.907 Thread 0x000002c266cd2a10 DEOPT PACKING pc=0x000002c2129b0c18 sp=0x000000e213dfe800
Event: 5.907 Thread 0x000002c266cd2a10 DEOPT UNPACKING pc=0x000002c2123c6da2 sp=0x000000e213dfe770 mode 2
Event: 5.946 Thread 0x000002c266cd2a10 Uncommon trap: trap_request=0xffffffcc fr.pc=0x000002c21299374c relative=0x0000000000002c4c
Event: 5.946 Thread 0x000002c266cd2a10 Uncommon trap: reason=intrinsic_or_type_checked_inlining action=make_not_entrant pc=0x000002c21299374c method=java.util.ArrayList$SubList.toArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 58 c2
Event: 5.946 Thread 0x000002c266cd2a10 DEOPT PACKING pc=0x000002c21299374c sp=0x000000e213dfb070
Event: 5.946 Thread 0x000002c266cd2a10 DEOPT UNPACKING pc=0x000002c2123c6da2 sp=0x000000e213dfae30 mode 2

Classes loaded (20 events):
Event: 4.885 Loading class java/util/concurrent/CompletableFuture$UniApply
Event: 4.885 Loading class java/util/concurrent/CompletableFuture$UniCompletion
Event: 4.885 Loading class java/util/concurrent/CompletableFuture$UniCompletion done
Event: 4.885 Loading class java/util/concurrent/CompletableFuture$UniApply done
Event: 4.887 Loading class java/util/concurrent/CancellationException
Event: 4.887 Loading class java/util/concurrent/CancellationException done
Event: 4.888 Loading class java/util/concurrent/CompletableFuture$UniAccept
Event: 4.888 Loading class java/util/concurrent/CompletableFuture$UniAccept done
Event: 4.888 Loading class java/util/concurrent/CompletableFuture$UniExceptionally
Event: 4.889 Loading class java/util/concurrent/CompletableFuture$UniExceptionally done
Event: 5.327 Loading class java/io/FileReader
Event: 5.327 Loading class java/io/FileReader done
Event: 5.328 Loading class java/lang/UnsupportedClassVersionError
Event: 5.328 Loading class java/lang/UnsupportedClassVersionError done
Event: 5.450 Loading class java/util/stream/ReduceOps$4
Event: 5.450 Loading class java/util/stream/ReduceOps$4 done
Event: 5.450 Loading class java/util/stream/ReduceOps$4ReducingSink
Event: 5.451 Loading class java/util/stream/ReduceOps$4ReducingSink done
Event: 5.649 Loading class java/util/regex/Pattern$Pos
Event: 5.649 Loading class java/util/regex/Pattern$Pos done

Classes unloaded (7 events):
Event: 2.301 Thread 0x000002c205a7e950 Unloading class 0x000002c22018b800 'java/lang/invoke/LambdaForm$MH+0x000002c22018b800'
Event: 2.301 Thread 0x000002c205a7e950 Unloading class 0x000002c22018b400 'java/lang/invoke/LambdaForm$MH+0x000002c22018b400'
Event: 2.301 Thread 0x000002c205a7e950 Unloading class 0x000002c22018b000 'java/lang/invoke/LambdaForm$MH+0x000002c22018b000'
Event: 2.301 Thread 0x000002c205a7e950 Unloading class 0x000002c22018ac00 'java/lang/invoke/LambdaForm$MH+0x000002c22018ac00'
Event: 2.301 Thread 0x000002c205a7e950 Unloading class 0x000002c22018a800 'java/lang/invoke/LambdaForm$BMH+0x000002c22018a800'
Event: 2.301 Thread 0x000002c205a7e950 Unloading class 0x000002c22018a400 'java/lang/invoke/LambdaForm$DMH+0x000002c22018a400'
Event: 2.301 Thread 0x000002c205a7e950 Unloading class 0x000002c220189000 'java/lang/invoke/LambdaForm$DMH+0x000002c220189000'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 3.411 Thread 0x000002c268a716b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d653cb78}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d653cb78) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.412 Thread 0x000002c268a716b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6541ff0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d6541ff0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.418 Thread 0x000002c268a716b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6581ec8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d6581ec8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.419 Thread 0x000002c268a716b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6586270}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int)'> (0x00000000d6586270) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.419 Thread 0x000002c268a716b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d658da18}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x00000000d658da18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.420 Thread 0x000002c268a716b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6594760}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x00000000d6594760) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.421 Thread 0x000002c268a716b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d659b780}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x00000000d659b780) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.421 Thread 0x000002c268a716b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d659ffd0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x00000000d659ffd0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.424 Thread 0x000002c268a716b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65bef18}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object)'> (0x00000000d65bef18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.878 Thread 0x000002c268a716b0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d61ee008}: Found class java.lang.Object, but interface was expected> (0x00000000d61ee008) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 4.913 Thread 0x000002c268a716b0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d622f3b0}: Found class java.lang.Object, but interface was expected> (0x00000000d622f3b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 5.294 Thread 0x000002c266cd2a10 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d630bc08}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, long)'> (0x00000000d630bc08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 5.316 Thread 0x000002c268a6dba0 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'{0x00000000d6433e48}> (0x00000000d6433e48) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 625]
Event: 5.349 Thread 0x000002c268a72a60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65a0628}: 'int java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object)'> (0x00000000d65a0628) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 5.398 Thread 0x000002c268a72a60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d674c0d8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x00000000d674c0d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 5.398 Thread 0x000002c268a72a60 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d674f3a8}: Found class java.lang.Object, but interface was expected> (0x00000000d674f3a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 5.454 Thread 0x000002c268a72a60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6a54448}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d6a54448) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 5.462 Thread 0x000002c268a72a60 Exception <a 'java/io/FileNotFoundException'{0x00000000d55c2b50}> (0x00000000d55c2b50) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 5.462 Thread 0x000002c268a72a60 Exception <a 'java/io/FileNotFoundException'{0x00000000d55c3b48}> (0x00000000d55c3b48) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 5.902 Thread 0x000002c268a72a60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5cd7598}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5cd7598) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 4.551 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 4.553 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 4.605 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 4.606 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 5.457 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 5.459 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 5.466 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.466 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.616 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.616 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.629 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.629 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.753 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.753 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.757 Executing VM operation: ICBufferFull
Event: 5.758 Executing VM operation: ICBufferFull done
Event: 5.828 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 5.831 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 5.945 Executing VM operation: ICBufferFull
Event: 5.946 Executing VM operation: ICBufferFull done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b25b710
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b25bb90
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b25ea90
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b28a510
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b28af10
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b28e410
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b2ad490
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b2b9590
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b2bc510
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b2c4e10
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b2ebe10
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b2f0010
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b2f0d10
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b33a590
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b33b790
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b343d10
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b34a210
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b34a710
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b34d110
Event: 4.374 Thread 0x000002c205a7e950 flushing  nmethod 0x000002c20b350590

Events (20 events):
Event: 2.808 Thread 0x000002c207c548f0 Thread added: 0x000002c266ce9250
Event: 2.808 Thread 0x000002c207c548f0 Thread added: 0x000002c266ce7ea0
Event: 2.808 Thread 0x000002c207c548f0 Thread added: 0x000002c266ceac90
Event: 2.809 Thread 0x000002c207c548f0 Thread added: 0x000002c266ce8530
Event: 2.809 Thread 0x000002c207c548f0 Thread added: 0x000002c268a6c7f0
Event: 2.809 Thread 0x000002c207c548f0 Thread added: 0x000002c268a6c160
Event: 2.809 Thread 0x000002c207c548f0 Thread added: 0x000002c268a6e8c0
Event: 2.809 Thread 0x000002c207c548f0 Thread added: 0x000002c268a6d510
Event: 2.809 Thread 0x000002c207c548f0 Thread added: 0x000002c268a6ce80
Event: 2.833 Thread 0x000002c207c548f0 Thread added: 0x000002c268a6ef50
Event: 2.968 Thread 0x000002c266cd2a10 Thread added: 0x000002c268a6f5e0
Event: 3.021 Thread 0x000002c207c548f0 Thread added: 0x000002c268a723d0
Event: 3.021 Thread 0x000002c207c548f0 Thread added: 0x000002c268a716b0
Event: 4.888 Thread 0x000002c268a716b0 Thread added: 0x000002c268a6dba0
Event: 4.916 Thread 0x000002c268a716b0 Thread added: 0x000002c268a6fc70
Event: 4.917 Thread 0x000002c268a716b0 Thread added: 0x000002c268a72a60
Event: 5.290 Thread 0x000002c260baf6d0 Thread exited: 0x000002c260baf6d0
Event: 5.291 Thread 0x000002c260bac050 Thread exited: 0x000002c260bac050
Event: 5.317 Thread 0x000002c207d056c0 Thread added: 0x000002c268d66570
Event: 5.323 Thread 0x000002c207d056c0 Thread added: 0x000002c268d64a30


Dynamic libraries:
0x00007ff7195b0000 - 0x00007ff7195be000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.exe
0x00007ffb67ed0000 - 0x00007ffb680e7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb66ef0000 - 0x00007ffb66fb4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb656e0000 - 0x00007ffb65ab0000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb651d0000 - 0x00007ffb652e1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffb485e0000 - 0x00007ffb485fe000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffb48380000 - 0x00007ffb48398000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jli.dll
0x00007ffb66c10000 - 0x00007ffb66dc1000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb4af60000 - 0x00007ffb4b1fc000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe\COMCTL32.dll
0x00007ffb65b30000 - 0x00007ffb65b56000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb67c50000 - 0x00007ffb67cf7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb67e60000 - 0x00007ffb67e89000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb65070000 - 0x00007ffb65193000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb64fd0000 - 0x00007ffb6506a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb65b60000 - 0x00007ffb65b91000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffb4a670000 - 0x00007ffb4a67c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffb48010000 - 0x00007ffb4809d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\msvcp140.dll
0x00007ffaeac70000 - 0x00007ffaeba07000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server\jvm.dll
0x00007ffb66b50000 - 0x00007ffb66c01000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb66fc0000 - 0x00007ffb67068000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb651a0000 - 0x00007ffb651c8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffb66dd0000 - 0x00007ffb66ee8000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb67a30000 - 0x00007ffb67aa1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb63db0000 - 0x00007ffb63dfd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffb62690000 - 0x00007ffb6269a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb5af50000 - 0x00007ffb5af84000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb63d90000 - 0x00007ffb63da3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffb63ff0000 - 0x00007ffb64008000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffb4a4f0000 - 0x00007ffb4a4fa000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
0x00007ffb61d80000 - 0x00007ffb61fb3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb66690000 - 0x00007ffb66a21000 	C:\WINDOWS\System32\combase.dll
0x00007ffb67070000 - 0x00007ffb67148000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffb4f720000 - 0x00007ffb4f752000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb653b0000 - 0x00007ffb6542b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffb4a160000 - 0x00007ffb4a16f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
0x00007ffb48340000 - 0x00007ffb4835f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
0x00007ffb65ba0000 - 0x00007ffb66441000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffb65430000 - 0x00007ffb6556f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffb62e60000 - 0x00007ffb6377a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffb67400000 - 0x00007ffb6750a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffb66ae0000 - 0x00007ffb66b49000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffb64f00000 - 0x00007ffb64f25000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffb48320000 - 0x00007ffb48338000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
0x00007ffb4a0f0000 - 0x00007ffb4a100000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
0x00007ffb5e820000 - 0x00007ffb5e94c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffb64530000 - 0x00007ffb64599000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffb48240000 - 0x00007ffb48256000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
0x00007ffb495b0000 - 0x00007ffb495c0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
0x00007ffb49230000 - 0x00007ffb49274000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
0x00007ffb67ab0000 - 0x00007ffb67c50000 	C:\WINDOWS\System32\ole32.dll
0x00007ffb647c0000 - 0x00007ffb647db000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffb63fb0000 - 0x00007ffb63fe7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffb64640000 - 0x00007ffb64668000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffb647e0000 - 0x00007ffb647ec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffb63a50000 - 0x00007ffb63a7d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffb67160000 - 0x00007ffb67169000 	C:\WINDOWS\System32\NSI.dll
0x00007ffb1a5c0000 - 0x00007ffb1a609000 	C:\Users\<USER>\AppData\Local\Temp\jna-71916\jna16087846074989327254.dll
0x00007ffb673e0000 - 0x00007ffb673e8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffb5e9f0000 - 0x00007ffb5ea09000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffb5e600000 - 0x00007ffb5e61f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL

JVMTI agents:
c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar path:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll, loaded, initialized, instrumentlib options:none

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854;C:\Users\<USER>\AppData\Local\Temp\jna-71916

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java\ss_ws --pipe=\\.\pipe\lsp-61e1700b38113ac45772b3ad35a4d435-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytesseract;C:\Program Files\Common Files\Oracle\Java\javapath;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\TortoiseSVN\bin;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Cloudflare\Cloudflare WARP\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Docker\Docker\resources\bin;D:\A\Git\Git\cmd;C:\Program Files\CMake\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;D:\A\Anaconda;D:\A\Anaconda\Library\mingw-w64\bin;D:\A\Anaconda\Library\usr\bin;D:\A\Anaconda\Library\bin;D:\A\Anaconda\Scripts;D:\A\Scripts\;D:\A\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin
USERNAME=HUY
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 0 days 5:27 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x3c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for the first 12 processors :
  Max Mhz: 2688, Current Mhz: 2688, Mhz Limit: 2688

Memory: 4k page, system-wide physical 16163M (1982M free)
TotalPageFile size 20259M (AvailPageFile size 3M)
current process WorkingSet (physical memory assigned to process): 244M, peak: 244M
current process commit charge ("private bytes"): 359M, peak: 361M

vm_info: OpenJDK 64-Bit Server VM (21.0.8+9-LTS) for windows-amd64 JRE (21.0.8+9-LTS), built on 2025-07-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
