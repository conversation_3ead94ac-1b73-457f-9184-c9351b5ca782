#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1618896 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=12456, tid=21828
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.8+9 (21.0.8+9) (build 21.0.8+9-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.8+9 (21.0.8+9-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java\ss_ws --pipe=\\.\pipe\lsp-7393c721c939eaeaff8fb765d5551878-sock

Host: 11th Gen Intel(R) Core(TM) i5-11400H @ 2.70GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Thu Sep 25 03:08:44 2025 SE Asia Standard Time elapsed time: 7.612234 seconds (0d 0h 0m 7s)

---------------  T H R E A D  ---------------

Current thread (0x00000204aa2b5d20):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=21828, stack(0x0000006593d00000,0x0000006593e00000) (1024K)]


Current CompileTask:
C2:7612 6130       4       org.eclipse.jdt.internal.compiler.lookup.BinaryTypeBinding::createMethod (1666 bytes)

Stack: [0x0000006593d00000,0x0000006593e00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d2449]
V  [jvm.dll+0x8ae341]
V  [jvm.dll+0x8b08be]
V  [jvm.dll+0x8b0fa3]
V  [jvm.dll+0x280c96]
V  [jvm.dll+0xc581d]
V  [jvm.dll+0xc5d53]
V  [jvm.dll+0x3b9162]
V  [jvm.dll+0x1e1213]
V  [jvm.dll+0x249022]
V  [jvm.dll+0x2484af]
V  [jvm.dll+0x1c89ee]
V  [jvm.dll+0x257d4d]
V  [jvm.dll+0x2562ea]
V  [jvm.dll+0x3f2d16]
V  [jvm.dll+0x857e6b]
V  [jvm.dll+0x6d0b0d]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002050be832a0, length=40, elements={
0x00000204aa1fefe0, 0x00000204aa29ffb0, 0x00000204aa2a1320, 0x00000204aa2a6000,
0x00000204aa2a6f10, 0x00000204aa2a96c0, 0x00000204aa2ad630, 0x00000204aa2b5d20,
0x0000020503042cb0, 0x000002050310e030, 0x00000205032f29f0, 0x00000205089e1d30,
0x0000020508a8dab0, 0x0000020508a8e110, 0x00000205087462c0, 0x0000020508909c60,
0x00000205089f86f0, 0x0000020508973d40, 0x0000020508e1ce80, 0x0000020508e1bad0,
0x0000020508e1e230, 0x0000020508e1dba0, 0x0000020508e1d510, 0x0000020508e1c7f0,
0x0000020508e1c160, 0x0000020508e1b440, 0x0000020508e1adb0, 0x00000205095b0a40,
0x00000205095ac210, 0x00000205095acf30, 0x00000205095b1760, 0x00000205095ad5c0,
0x00000205095af000, 0x00000205095b03b0, 0x00000205095adc50, 0x00000205095ae2e0,
0x0000020508f79430, 0x0000020508716550, 0x00000205095b1df0, 0x00000205095ac8a0
}

Java Threads: ( => current thread )
  0x00000204aa1fefe0 JavaThread "main"                              [_thread_blocked, id=20860, stack(0x0000006593300000,0x0000006593400000) (1024K)]
  0x00000204aa29ffb0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=22348, stack(0x0000006593700000,0x0000006593800000) (1024K)]
  0x00000204aa2a1320 JavaThread "Finalizer"                  daemon [_thread_blocked, id=17000, stack(0x0000006593800000,0x0000006593900000) (1024K)]
  0x00000204aa2a6000 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=18636, stack(0x0000006593900000,0x0000006593a00000) (1024K)]
  0x00000204aa2a6f10 JavaThread "Attach Listener"            daemon [_thread_blocked, id=18756, stack(0x0000006593a00000,0x0000006593b00000) (1024K)]
  0x00000204aa2a96c0 JavaThread "Service Thread"             daemon [_thread_blocked, id=21348, stack(0x0000006593b00000,0x0000006593c00000) (1024K)]
  0x00000204aa2ad630 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=14280, stack(0x0000006593c00000,0x0000006593d00000) (1024K)]
=>0x00000204aa2b5d20 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=21828, stack(0x0000006593d00000,0x0000006593e00000) (1024K)]
  0x0000020503042cb0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=23496, stack(0x0000006593e00000,0x0000006593f00000) (1024K)]
  0x000002050310e030 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=9460, stack(0x0000006593f00000,0x0000006594000000) (1024K)]
  0x00000205032f29f0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=22220, stack(0x0000006594100000,0x0000006594200000) (1024K)]
  0x00000205089e1d30 JavaThread "Active Thread: Equinox Container: 7be4204a-0230-4757-8efa-32102ea768ec"        [_thread_blocked, id=8108, stack(0x0000006594900000,0x0000006594a00000) (1024K)]
  0x0000020508a8dab0 JavaThread "Framework Event Dispatcher: Equinox Container: 7be4204a-0230-4757-8efa-32102ea768ec" daemon [_thread_blocked, id=2768, stack(0x0000006594a00000,0x0000006594b00000) (1024K)]
  0x0000020508a8e110 JavaThread "Start Level: Equinox Container: 7be4204a-0230-4757-8efa-32102ea768ec" daemon [_thread_blocked, id=7424, stack(0x0000006594b00000,0x0000006594c00000) (1024K)]
  0x00000205087462c0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=23432, stack(0x0000006594d00000,0x0000006594e00000) (1024K)]
  0x0000020508909c60 JavaThread "Worker-JM"                         [_thread_blocked, id=22208, stack(0x0000006594f00000,0x0000006595000000) (1024K)]
  0x00000205089f86f0 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=19608, stack(0x0000006594000000,0x0000006594100000) (1024K)]
  0x0000020508973d40 JavaThread "Worker-0"                          [_thread_blocked, id=21288, stack(0x0000006594200000,0x0000006594300000) (1024K)]
  0x0000020508e1ce80 JavaThread "Worker-1"                          [_thread_blocked, id=22632, stack(0x0000006595200000,0x0000006595300000) (1024K)]
  0x0000020508e1bad0 JavaThread "Thread-2"                   daemon [_thread_in_native, id=8848, stack(0x0000006595400000,0x0000006595500000) (1024K)]
  0x0000020508e1e230 JavaThread "Thread-3"                   daemon [_thread_in_native, id=18332, stack(0x0000006595500000,0x0000006595600000) (1024K)]
  0x0000020508e1dba0 JavaThread "Thread-4"                   daemon [_thread_in_native, id=23240, stack(0x0000006595600000,0x0000006595700000) (1024K)]
  0x0000020508e1d510 JavaThread "Thread-5"                   daemon [_thread_in_native, id=15396, stack(0x0000006595700000,0x0000006595800000) (1024K)]
  0x0000020508e1c7f0 JavaThread "Thread-6"                   daemon [_thread_in_native, id=22900, stack(0x0000006595800000,0x0000006595900000) (1024K)]
  0x0000020508e1c160 JavaThread "Thread-7"                   daemon [_thread_in_native, id=12112, stack(0x0000006595900000,0x0000006595a00000) (1024K)]
  0x0000020508e1b440 JavaThread "Thread-8"                   daemon [_thread_in_native, id=18056, stack(0x0000006595a00000,0x0000006595b00000) (1024K)]
  0x0000020508e1adb0 JavaThread "Thread-9"                   daemon [_thread_in_native, id=4880, stack(0x0000006595b00000,0x0000006595c00000) (1024K)]
  0x00000205095b0a40 JavaThread "Thread-10"                  daemon [_thread_in_native, id=5228, stack(0x0000006595c00000,0x0000006595d00000) (1024K)]
  0x00000205095ac210 JavaThread "Thread-11"                  daemon [_thread_in_native, id=4816, stack(0x0000006595d00000,0x0000006595e00000) (1024K)]
  0x00000205095acf30 JavaThread "Thread-12"                  daemon [_thread_in_native, id=9696, stack(0x0000006595e00000,0x0000006595f00000) (1024K)]
  0x00000205095b1760 JavaThread "Thread-13"                  daemon [_thread_in_native, id=15792, stack(0x0000006595f00000,0x0000006596000000) (1024K)]
  0x00000205095ad5c0 JavaThread "Thread-14"                  daemon [_thread_in_native, id=23232, stack(0x0000006596000000,0x0000006596100000) (1024K)]
  0x00000205095af000 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=14240, stack(0x0000006596100000,0x0000006596200000) (1024K)]
  0x00000205095b03b0 JavaThread "Worker-2"                          [_thread_blocked, id=19236, stack(0x0000006595300000,0x0000006595400000) (1024K)]
  0x00000205095adc50 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=20492, stack(0x0000006596200000,0x0000006596300000) (1024K)]
  0x00000205095ae2e0 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=19932, stack(0x0000006596300000,0x0000006596400000) (1024K)]
  0x0000020508f79430 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=23420, stack(0x0000006596400000,0x0000006596500000) (1024K)]
  0x0000020508716550 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=22260, stack(0x0000006596500000,0x0000006596600000) (1024K)]
  0x00000205095b1df0 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=21132, stack(0x0000006596600000,0x0000006596700000) (1024K)]
  0x00000205095ac8a0 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=3224, stack(0x0000006596700000,0x0000006596800000) (1024K)]
Total: 40

Other Threads:
  0x00000204aa29fc10 VMThread "VM Thread"                           [id=1332, stack(0x0000006593600000,0x0000006593700000) (1024K)]
  0x00000204aa26ebe0 WatcherThread "VM Periodic Task Thread"        [id=4148, stack(0x0000006593500000,0x0000006593600000) (1024K)]
  0x00000204aa220800 WorkerThread "GC Thread#0"                     [id=5912, stack(0x0000006593400000,0x0000006593500000) (1024K)]
  0x0000020508376270 WorkerThread "GC Thread#1"                     [id=16864, stack(0x0000006594300000,0x0000006594400000) (1024K)]
  0x0000020508374ad0 WorkerThread "GC Thread#2"                     [id=14644, stack(0x0000006594400000,0x0000006594500000) (1024K)]
  0x000002050828d0d0 WorkerThread "GC Thread#3"                     [id=21616, stack(0x0000006594500000,0x0000006594600000) (1024K)]
  0x000002050828d870 WorkerThread "GC Thread#4"                     [id=16812, stack(0x0000006594600000,0x0000006594700000) (1024K)]
  0x0000020508486b30 WorkerThread "GC Thread#5"                     [id=10120, stack(0x0000006594700000,0x0000006594800000) (1024K)]
  0x00000205082f2ca0 WorkerThread "GC Thread#6"                     [id=3640, stack(0x0000006594800000,0x0000006594900000) (1024K)]
  0x0000020508393c90 WorkerThread "GC Thread#7"                     [id=18408, stack(0x0000006594c00000,0x0000006594d00000) (1024K)]
  0x0000020508c07dd0 WorkerThread "GC Thread#8"                     [id=6404, stack(0x0000006595000000,0x0000006595100000) (1024K)]
  0x0000020508c46cf0 WorkerThread "GC Thread#9"                     [id=23348, stack(0x0000006595100000,0x0000006595200000) (1024K)]
Total: 12

Threads with active compile tasks:
C2 CompilerThread0  7625 6130       4       org.eclipse.jdt.internal.compiler.lookup.BinaryTypeBinding::createMethod (1666 bytes)
C2 CompilerThread1  7625 5830   !   4       lombok.core.FieldAugment$ReflectionFieldAugment::getAndSet (80 bytes)
C2 CompilerThread2  7625 5818       4       lombok.core.FieldAugment::set (8 bytes)
Total: 3

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x00000204c1000000-0x00000204c1ba0000-0x00000204c1ba0000), size 12189696, SharedBaseAddress: 0x00000204c1000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000204c2000000-0x0000020502000000, reserved size: 1073741824
Narrow klass base: 0x00000204c1000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 12 total, 12 available
 Memory: 16163M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 10

Heap:
 PSYoungGen      total 19968K, used 9526K [0x00000000d5580000, 0x00000000d6b80000, 0x0000000100000000)
  eden space 17408K, 40% used [0x00000000d5580000,0x00000000d5c69ba0,0x00000000d6680000)
  from space 2560K, 95% used [0x00000000d6900000,0x00000000d6b64030,0x00000000d6b80000)
  to   space 2560K, 0% used [0x00000000d6680000,0x00000000d6680000,0x00000000d6900000)
 ParOldGen       total 68608K, used 38536K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 56% used [0x0000000080000000,0x00000000825a2388,0x0000000084300000)
 Metaspace       used 43528K, committed 44544K, reserved 1114112K
  class space    used 4285K, committed 4736K, reserved 1048576K

Card table byte_map: [0x00000204a9ba0000,0x00000204a9fb0000] _byte_map_base: 0x00000204a97a0000

Marking Bits: (ParMarkBitMap*) 0x00007ffaeb92a340
 Begin Bits: [0x00000204bc6a0000, 0x00000204be6a0000)
 End Bits:   [0x00000204be6a0000, 0x00000204c06a0000)

Polling page: 0x00000204a7e90000

Metaspace:

Usage:
  Non-class:     38.32 MB used.
      Class:      4.19 MB used.
       Both:     42.51 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      38.88 MB ( 61%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       4.62 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      43.50 MB (  4%) committed. 

Chunk freelists:
   Non-Class:  9.01 MB
       Class:  11.34 MB
        Both:  20.36 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 58.38 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 860.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 696.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 20.
num_chunks_taken_from_freelist: 2734.
num_chunk_merges: 11.
num_chunk_splits: 1719.
num_chunks_enlarged: 1071.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=3778Kb max_used=3778Kb free=116221Kb
 bounds [0x00000204b4ec0000, 0x00000204b5280000, 0x00000204bc3f0000]
CodeHeap 'profiled nmethods': size=120000Kb used=14510Kb max_used=14510Kb free=105489Kb
 bounds [0x00000204ad3f0000, 0x00000204ae220000, 0x00000204b4920000]
CodeHeap 'non-nmethods': size=5760Kb used=1417Kb max_used=1507Kb free=4342Kb
 bounds [0x00000204b4920000, 0x00000204b4b90000, 0x00000204b4ec0000]
CodeCache: size=245760Kb, used=19705Kb, max_used=19795Kb, free=226052Kb
 total_blobs=6993, nmethods=6276, adapters=622, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 7.534 Thread 0x0000020503042cb0 6714       2       org.eclipse.jdt.internal.compiler.impl.CompilerOptions::getSeverity (74 bytes)
Event: 7.534 Thread 0x0000020503042cb0 nmethod 6714 0x00000204ae216510 code [0x00000204ae2166e0, 0x00000204ae2169d8]
Event: 7.534 Thread 0x0000020503042cb0 6719       2       org.eclipse.jdt.internal.compiler.problem.ProblemReporter::computeSeverity (602 bytes)
Event: 7.535 Thread 0x0000020503042cb0 nmethod 6719 0x00000204ae216c10 code [0x00000204ae216e40, 0x00000204ae217488]
Event: 7.535 Thread 0x0000020503042cb0 6712       2       lombok.eclipse.agent.PatchExtensionMethod::getTypeNode (47 bytes)
Event: 7.535 Thread 0x0000020503042cb0 nmethod 6712 0x00000204ae217690 code [0x00000204ae217880, 0x00000204ae217b40]
Event: 7.535 Thread 0x0000020503042cb0 6708       2       java.lang.invoke.LambdaForm$MH/0x00000204c2490400::invoke (42 bytes)
Event: 7.535 Thread 0x0000020503042cb0 nmethod 6708 0x00000204ae217d90 code [0x00000204ae217f60, 0x00000204ae218240]
Event: 7.535 Thread 0x0000020503042cb0 6721       2       java.util.Locale::hasExtensions (13 bytes)
Event: 7.536 Thread 0x0000020503042cb0 nmethod 6721 0x00000204ae218490 code [0x00000204ae218620, 0x00000204ae218738]
Event: 7.536 Thread 0x0000020503042cb0 6718       2       org.eclipse.jdt.internal.compiler.lookup.ReferenceBinding::canBeSeenBy (217 bytes)
Event: 7.536 Thread 0x0000020503042cb0 nmethod 6718 0x00000204ae218810 code [0x00000204ae218ae0, 0x00000204ae219448]
Event: 7.536 Thread 0x0000020503042cb0 6722       1       java.text.NumberFormat::isGroupingUsed (5 bytes)
Event: 7.536 Thread 0x0000020503042cb0 nmethod 6722 0x00000204b5270190 code [0x00000204b5270320, 0x00000204b52703f0]
Event: 7.536 Thread 0x0000020503042cb0 6713       1       org.eclipse.jdt.internal.compiler.env.NameEnvironmentAnswer::getBinaryType (5 bytes)
Event: 7.536 Thread 0x0000020503042cb0 nmethod 6713 0x00000204b5270490 code [0x00000204b5270620, 0x00000204b52706e8]
Event: 7.537 Thread 0x0000020503042cb0 6723       2       java.lang.invoke.VarHandleGuards::guard_LLL_Z (86 bytes)
Event: 7.538 Thread 0x0000020503042cb0 nmethod 6723 0x00000204ae219910 code [0x00000204ae219ba0, 0x00000204ae21a4a8]
Event: 7.538 Thread 0x0000020503042cb0 6724       2       java.util.concurrent.ConcurrentLinkedQueue::updateHead (26 bytes)
Event: 7.539 Thread 0x0000020503042cb0 nmethod 6724 0x00000204ae21aa90 code [0x00000204ae21ace0, 0x00000204ae21b480]

GC Heap History (20 events):
Event: 5.418 GC heap before
{Heap before GC invocations=19 (full 1):
 PSYoungGen      total 21504K, used 10937K [0x00000000d5580000, 0x00000000d6d00000, 0x0000000100000000)
  eden space 20480K, 50% used [0x00000000d5580000,0x00000000d5f8cf98,0x00000000d6980000)
  from space 1024K, 63% used [0x00000000d6a80000,0x00000000d6b21558,0x00000000d6b80000)
  to   space 1536K, 0% used [0x00000000d6b80000,0x00000000d6b80000,0x00000000d6d00000)
 ParOldGen       total 68608K, used 29757K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 43% used [0x0000000080000000,0x0000000081d0f668,0x0000000084300000)
 Metaspace       used 34999K, committed 35840K, reserved 1114112K
  class space    used 3591K, committed 3968K, reserved 1048576K
}
Event: 5.419 GC heap after
{Heap after GC invocations=19 (full 1):
 PSYoungGen      total 20992K, used 293K [0x00000000d5580000, 0x00000000d6c00000, 0x0000000100000000)
  eden space 20480K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6980000)
  from space 512K, 57% used [0x00000000d6b80000,0x00000000d6bc97a0,0x00000000d6c00000)
  to   space 512K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6b80000)
 ParOldGen       total 68608K, used 30258K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 44% used [0x0000000080000000,0x0000000081d8c8c8,0x0000000084300000)
 Metaspace       used 34999K, committed 35840K, reserved 1114112K
  class space    used 3591K, committed 3968K, reserved 1048576K
}
Event: 5.419 GC heap before
{Heap before GC invocations=20 (full 2):
 PSYoungGen      total 20992K, used 293K [0x00000000d5580000, 0x00000000d6c00000, 0x0000000100000000)
  eden space 20480K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6980000)
  from space 512K, 57% used [0x00000000d6b80000,0x00000000d6bc97a0,0x00000000d6c00000)
  to   space 512K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6b80000)
 ParOldGen       total 68608K, used 30258K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 44% used [0x0000000080000000,0x0000000081d8c8c8,0x0000000084300000)
 Metaspace       used 34999K, committed 35840K, reserved 1114112K
  class space    used 3591K, committed 3968K, reserved 1048576K
}
Event: 5.460 GC heap after
{Heap after GC invocations=20 (full 2):
 PSYoungGen      total 20992K, used 0K [0x00000000d5580000, 0x00000000d6c00000, 0x0000000100000000)
  eden space 20480K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6980000)
  from space 512K, 0% used [0x00000000d6b80000,0x00000000d6b80000,0x00000000d6c00000)
  to   space 512K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6b80000)
 ParOldGen       total 68608K, used 29187K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 42% used [0x0000000080000000,0x0000000081c80f48,0x0000000084300000)
 Metaspace       used 34999K, committed 35840K, reserved 1114112K
  class space    used 3591K, committed 3968K, reserved 1048576K
}
Event: 5.591 GC heap before
{Heap before GC invocations=21 (full 2):
 PSYoungGen      total 20992K, used 20414K [0x00000000d5580000, 0x00000000d6c00000, 0x0000000100000000)
  eden space 20480K, 99% used [0x00000000d5580000,0x00000000d696f930,0x00000000d6980000)
  from space 512K, 0% used [0x00000000d6b80000,0x00000000d6b80000,0x00000000d6c00000)
  to   space 512K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6b80000)
 ParOldGen       total 68608K, used 29187K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 42% used [0x0000000080000000,0x0000000081c80f48,0x0000000084300000)
 Metaspace       used 35816K, committed 36672K, reserved 1114112K
  class space    used 3658K, committed 4032K, reserved 1048576K
}
Event: 5.592 GC heap after
{Heap after GC invocations=21 (full 2):
 PSYoungGen      total 20992K, used 482K [0x00000000d5580000, 0x00000000d6d80000, 0x0000000100000000)
  eden space 20480K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6980000)
  from space 512K, 94% used [0x00000000d6b00000,0x00000000d6b78b88,0x00000000d6b80000)
  to   space 2048K, 0% used [0x00000000d6b80000,0x00000000d6b80000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 30476K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 44% used [0x0000000080000000,0x0000000081dc3148,0x0000000084300000)
 Metaspace       used 35816K, committed 36672K, reserved 1114112K
  class space    used 3658K, committed 4032K, reserved 1048576K
}
Event: 5.647 GC heap before
{Heap before GC invocations=22 (full 2):
 PSYoungGen      total 20992K, used 20962K [0x00000000d5580000, 0x00000000d6d80000, 0x0000000100000000)
  eden space 20480K, 100% used [0x00000000d5580000,0x00000000d6980000,0x00000000d6980000)
  from space 512K, 94% used [0x00000000d6b00000,0x00000000d6b78b88,0x00000000d6b80000)
  to   space 2048K, 0% used [0x00000000d6b80000,0x00000000d6b80000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 30476K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 44% used [0x0000000080000000,0x0000000081dc3148,0x0000000084300000)
 Metaspace       used 35825K, committed 36672K, reserved 1114112K
  class space    used 3658K, committed 4032K, reserved 1048576K
}
Event: 5.648 GC heap after
{Heap after GC invocations=22 (full 2):
 PSYoungGen      total 21504K, used 854K [0x00000000d5580000, 0x00000000d6c80000, 0x0000000100000000)
  eden space 20480K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6980000)
  from space 1024K, 83% used [0x00000000d6b80000,0x00000000d6c55aa8,0x00000000d6c80000)
  to   space 1536K, 0% used [0x00000000d6980000,0x00000000d6980000,0x00000000d6b00000)
 ParOldGen       total 68608K, used 31149K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 45% used [0x0000000080000000,0x0000000081e6b688,0x0000000084300000)
 Metaspace       used 35825K, committed 36672K, reserved 1114112K
  class space    used 3658K, committed 4032K, reserved 1048576K
}
Event: 6.530 GC heap before
{Heap before GC invocations=23 (full 2):
 PSYoungGen      total 21504K, used 21334K [0x00000000d5580000, 0x00000000d6c80000, 0x0000000100000000)
  eden space 20480K, 100% used [0x00000000d5580000,0x00000000d6980000,0x00000000d6980000)
  from space 1024K, 83% used [0x00000000d6b80000,0x00000000d6c55aa8,0x00000000d6c80000)
  to   space 1536K, 0% used [0x00000000d6980000,0x00000000d6980000,0x00000000d6b00000)
 ParOldGen       total 68608K, used 31149K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 45% used [0x0000000080000000,0x0000000081e6b688,0x0000000084300000)
 Metaspace       used 39168K, committed 40128K, reserved 1114112K
  class space    used 3931K, committed 4352K, reserved 1048576K
}
Event: 6.532 GC heap after
{Heap after GC invocations=23 (full 2):
 PSYoungGen      total 22016K, used 1238K [0x00000000d5580000, 0x00000000d6c80000, 0x0000000100000000)
  eden space 20480K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6980000)
  from space 1536K, 80% used [0x00000000d6980000,0x00000000d6ab5ab0,0x00000000d6b00000)
  to   space 1536K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6c80000)
 ParOldGen       total 68608K, used 31173K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 45% used [0x0000000080000000,0x0000000081e71688,0x0000000084300000)
 Metaspace       used 39168K, committed 40128K, reserved 1114112K
  class space    used 3931K, committed 4352K, reserved 1048576K
}
Event: 6.888 GC heap before
{Heap before GC invocations=24 (full 2):
 PSYoungGen      total 22016K, used 21718K [0x00000000d5580000, 0x00000000d6c80000, 0x0000000100000000)
  eden space 20480K, 100% used [0x00000000d5580000,0x00000000d6980000,0x00000000d6980000)
  from space 1536K, 80% used [0x00000000d6980000,0x00000000d6ab5ab0,0x00000000d6b00000)
  to   space 1536K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6c80000)
 ParOldGen       total 68608K, used 31173K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 45% used [0x0000000080000000,0x0000000081e71688,0x0000000084300000)
 Metaspace       used 40580K, committed 41536K, reserved 1114112K
  class space    used 4087K, committed 4480K, reserved 1048576K
}
Event: 6.889 GC heap after
{Heap after GC invocations=24 (full 2):
 PSYoungGen      total 20480K, used 1534K [0x00000000d5580000, 0x00000000d6f80000, 0x0000000100000000)
  eden space 18944K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6800000)
  from space 1536K, 99% used [0x00000000d6b00000,0x00000000d6c7f808,0x00000000d6c80000)
  to   space 3072K, 0% used [0x00000000d6800000,0x00000000d6800000,0x00000000d6b00000)
 ParOldGen       total 68608K, used 32361K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 47% used [0x0000000080000000,0x0000000081f9a638,0x0000000084300000)
 Metaspace       used 40580K, committed 41536K, reserved 1114112K
  class space    used 4087K, committed 4480K, reserved 1048576K
}
Event: 7.081 GC heap before
{Heap before GC invocations=25 (full 2):
 PSYoungGen      total 20480K, used 20478K [0x00000000d5580000, 0x00000000d6f80000, 0x0000000100000000)
  eden space 18944K, 100% used [0x00000000d5580000,0x00000000d6800000,0x00000000d6800000)
  from space 1536K, 99% used [0x00000000d6b00000,0x00000000d6c7f808,0x00000000d6c80000)
  to   space 3072K, 0% used [0x00000000d6800000,0x00000000d6800000,0x00000000d6b00000)
 ParOldGen       total 68608K, used 32361K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 47% used [0x0000000080000000,0x0000000081f9a638,0x0000000084300000)
 Metaspace       used 41136K, committed 42112K, reserved 1114112K
  class space    used 4118K, committed 4544K, reserved 1048576K
}
Event: 7.084 GC heap after
{Heap after GC invocations=25 (full 2):
 PSYoungGen      total 20480K, used 1264K [0x00000000d5580000, 0x00000000d6c00000, 0x0000000100000000)
  eden space 18944K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6800000)
  from space 1536K, 82% used [0x00000000d6800000,0x00000000d693c030,0x00000000d6980000)
  to   space 2048K, 0% used [0x00000000d6a00000,0x00000000d6a00000,0x00000000d6c00000)
 ParOldGen       total 68608K, used 33864K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 49% used [0x0000000080000000,0x0000000082112068,0x0000000084300000)
 Metaspace       used 41136K, committed 42112K, reserved 1114112K
  class space    used 4118K, committed 4544K, reserved 1048576K
}
Event: 7.214 GC heap before
{Heap before GC invocations=26 (full 2):
 PSYoungGen      total 20480K, used 20198K [0x00000000d5580000, 0x00000000d6c00000, 0x0000000100000000)
  eden space 18944K, 99% used [0x00000000d5580000,0x00000000d67fdba0,0x00000000d6800000)
  from space 1536K, 82% used [0x00000000d6800000,0x00000000d693c030,0x00000000d6980000)
  to   space 2048K, 0% used [0x00000000d6a00000,0x00000000d6a00000,0x00000000d6c00000)
 ParOldGen       total 68608K, used 33864K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 49% used [0x0000000080000000,0x0000000082112068,0x0000000084300000)
 Metaspace       used 41840K, committed 42816K, reserved 1114112K
  class space    used 4185K, committed 4608K, reserved 1048576K
}
Event: 7.215 GC heap after
{Heap after GC invocations=26 (full 2):
 PSYoungGen      total 19456K, used 2032K [0x00000000d5580000, 0x00000000d6f00000, 0x0000000100000000)
  eden space 17408K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6680000)
  from space 2048K, 99% used [0x00000000d6a00000,0x00000000d6bfc290,0x00000000d6c00000)
  to   space 3584K, 0% used [0x00000000d6680000,0x00000000d6680000,0x00000000d6a00000)
 ParOldGen       total 68608K, used 34947K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 50% used [0x0000000080000000,0x0000000082220fe8,0x0000000084300000)
 Metaspace       used 41840K, committed 42816K, reserved 1114112K
  class space    used 4185K, committed 4608K, reserved 1048576K
}
Event: 7.295 GC heap before
{Heap before GC invocations=27 (full 2):
 PSYoungGen      total 19456K, used 19440K [0x00000000d5580000, 0x00000000d6f00000, 0x0000000100000000)
  eden space 17408K, 100% used [0x00000000d5580000,0x00000000d6680000,0x00000000d6680000)
  from space 2048K, 99% used [0x00000000d6a00000,0x00000000d6bfc290,0x00000000d6c00000)
  to   space 3584K, 0% used [0x00000000d6680000,0x00000000d6680000,0x00000000d6a00000)
 ParOldGen       total 68608K, used 34947K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 50% used [0x0000000080000000,0x0000000082220fe8,0x0000000084300000)
 Metaspace       used 42341K, committed 43392K, reserved 1114112K
  class space    used 4221K, committed 4672K, reserved 1048576K
}
Event: 7.298 GC heap after
{Heap after GC invocations=27 (full 2):
 PSYoungGen      total 19456K, used 2001K [0x00000000d5580000, 0x00000000d6b80000, 0x0000000100000000)
  eden space 17408K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6680000)
  from space 2048K, 97% used [0x00000000d6680000,0x00000000d6874738,0x00000000d6880000)
  to   space 2560K, 0% used [0x00000000d6900000,0x00000000d6900000,0x00000000d6b80000)
 ParOldGen       total 68608K, used 36575K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 53% used [0x0000000080000000,0x00000000823b7e58,0x0000000084300000)
 Metaspace       used 42341K, committed 43392K, reserved 1114112K
  class space    used 4221K, committed 4672K, reserved 1048576K
}
Event: 7.463 GC heap before
{Heap before GC invocations=28 (full 2):
 PSYoungGen      total 19456K, used 19409K [0x00000000d5580000, 0x00000000d6b80000, 0x0000000100000000)
  eden space 17408K, 100% used [0x00000000d5580000,0x00000000d6680000,0x00000000d6680000)
  from space 2048K, 97% used [0x00000000d6680000,0x00000000d6874738,0x00000000d6880000)
  to   space 2560K, 0% used [0x00000000d6900000,0x00000000d6900000,0x00000000d6b80000)
 ParOldGen       total 68608K, used 36575K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 53% used [0x0000000080000000,0x00000000823b7e58,0x0000000084300000)
 Metaspace       used 43334K, committed 44352K, reserved 1114112K
  class space    used 4285K, committed 4736K, reserved 1048576K
}
Event: 7.466 GC heap after
{Heap after GC invocations=28 (full 2):
 PSYoungGen      total 19968K, used 2448K [0x00000000d5580000, 0x00000000d6b80000, 0x0000000100000000)
  eden space 17408K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6680000)
  from space 2560K, 95% used [0x00000000d6900000,0x00000000d6b64030,0x00000000d6b80000)
  to   space 2560K, 0% used [0x00000000d6680000,0x00000000d6680000,0x00000000d6900000)
 ParOldGen       total 68608K, used 38536K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 56% used [0x0000000080000000,0x00000000825a2388,0x0000000084300000)
 Metaspace       used 43334K, committed 44352K, reserved 1114112K
  class space    used 4285K, committed 4736K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.009 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
Event: 0.088 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.107 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
Event: 0.110 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
Event: 0.112 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
Event: 0.116 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.130 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
Event: 0.223 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
Event: 1.314 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
Event: 2.218 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-71916\jna13182268162738980999.dll

Deoptimization events (20 events):
Event: 7.349 Thread 0x00000205095ac8a0 DEOPT PACKING pc=0x00000204b4f01a60 sp=0x00000065967fd670
Event: 7.349 Thread 0x00000205095ac8a0 DEOPT UNPACKING pc=0x00000204b4976da2 sp=0x00000065967fd610 mode 2
Event: 7.368 Thread 0x0000020508973d40 DEOPT PACKING pc=0x00000204adefb5e5 sp=0x00000065942fdde0
Event: 7.368 Thread 0x0000020508973d40 DEOPT UNPACKING pc=0x00000204b49778e2 sp=0x00000065942fd298 mode 0
Event: 7.376 Thread 0x0000020508973d40 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x00000204b5258610 relative=0x0000000000000770
Event: 7.376 Thread 0x0000020508973d40 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x00000204b5258610 method=org.eclipse.jdt.internal.compiler.parser.Parser.consumeRule(I)V @ 7702 c2
Event: 7.376 Thread 0x0000020508973d40 DEOPT PACKING pc=0x00000204b5258610 sp=0x00000065942fdca0
Event: 7.376 Thread 0x0000020508973d40 DEOPT UNPACKING pc=0x00000204b4976da2 sp=0x00000065942fdc18 mode 2
Event: 7.376 Thread 0x0000020508973d40 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x00000204b5258610 relative=0x0000000000000770
Event: 7.376 Thread 0x0000020508973d40 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x00000204b5258610 method=org.eclipse.jdt.internal.compiler.parser.Parser.consumeRule(I)V @ 7702 c2
Event: 7.376 Thread 0x0000020508973d40 DEOPT PACKING pc=0x00000204b5258610 sp=0x00000065942fdca0
Event: 7.376 Thread 0x0000020508973d40 DEOPT UNPACKING pc=0x00000204b4976da2 sp=0x00000065942fdc18 mode 2
Event: 7.395 Thread 0x00000205095ac8a0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000204b4f8a7dc relative=0x000000000000019c
Event: 7.395 Thread 0x00000205095ac8a0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000204b4f8a7dc method=java.util.concurrent.ConcurrentHashMap.get(Ljava/lang/Object;)Ljava/lang/Object; @ 152 c2
Event: 7.395 Thread 0x00000205095ac8a0 DEOPT PACKING pc=0x00000204b4f8a7dc sp=0x00000065967fe920
Event: 7.395 Thread 0x00000205095ac8a0 DEOPT UNPACKING pc=0x00000204b4976da2 sp=0x00000065967fe898 mode 2
Event: 7.399 Thread 0x00000205095ac8a0 DEOPT PACKING pc=0x00000204adaf36e0 sp=0x00000065967fe530
Event: 7.399 Thread 0x00000205095ac8a0 DEOPT UNPACKING pc=0x00000204b49778e2 sp=0x00000065967fda28 mode 3
Event: 7.406 Thread 0x0000020508973d40 DEOPT PACKING pc=0x00000204adefb5e5 sp=0x00000065942fbcc0
Event: 7.406 Thread 0x0000020508973d40 DEOPT UNPACKING pc=0x00000204b49778e2 sp=0x00000065942fb178 mode 0

Classes loaded (20 events):
Event: 6.061 Loading class java/util/concurrent/CancellationException
Event: 6.061 Loading class java/util/concurrent/CancellationException done
Event: 6.063 Loading class java/util/concurrent/CompletableFuture$UniAccept
Event: 6.063 Loading class java/util/concurrent/CompletableFuture$UniAccept done
Event: 6.064 Loading class java/util/concurrent/CompletableFuture$UniExceptionally
Event: 6.064 Loading class java/util/concurrent/CompletableFuture$UniExceptionally done
Event: 6.473 Loading class java/lang/UnsupportedClassVersionError
Event: 6.473 Loading class java/lang/UnsupportedClassVersionError done
Event: 6.473 Loading class java/io/FileReader
Event: 6.473 Loading class java/io/FileReader done
Event: 6.596 Loading class java/util/stream/ReduceOps$4
Event: 6.596 Loading class java/util/stream/ReduceOps$4 done
Event: 6.596 Loading class java/util/stream/ReduceOps$4ReducingSink
Event: 6.596 Loading class java/util/stream/ReduceOps$4ReducingSink done
Event: 6.809 Loading class java/util/regex/Pattern$Pos
Event: 6.809 Loading class java/util/regex/Pattern$Pos done
Event: 7.396 Loading class java/util/concurrent/ConcurrentHashMap$TreeNode
Event: 7.398 Loading class java/util/concurrent/ConcurrentHashMap$TreeNode done
Event: 7.398 Loading class java/util/concurrent/ConcurrentHashMap$TreeBin
Event: 7.399 Loading class java/util/concurrent/ConcurrentHashMap$TreeBin done

Classes unloaded (7 events):
Event: 2.779 Thread 0x00000204aa29fc10 Unloading class 0x00000204c218c800 'java/lang/invoke/LambdaForm$MH+0x00000204c218c800'
Event: 2.779 Thread 0x00000204aa29fc10 Unloading class 0x00000204c218c400 'java/lang/invoke/LambdaForm$MH+0x00000204c218c400'
Event: 2.779 Thread 0x00000204aa29fc10 Unloading class 0x00000204c218c000 'java/lang/invoke/LambdaForm$MH+0x00000204c218c000'
Event: 2.779 Thread 0x00000204aa29fc10 Unloading class 0x00000204c218bc00 'java/lang/invoke/LambdaForm$MH+0x00000204c218bc00'
Event: 2.779 Thread 0x00000204aa29fc10 Unloading class 0x00000204c218b800 'java/lang/invoke/LambdaForm$BMH+0x00000204c218b800'
Event: 2.779 Thread 0x00000204aa29fc10 Unloading class 0x00000204c218b400 'java/lang/invoke/LambdaForm$DMH+0x00000204c218b400'
Event: 2.779 Thread 0x00000204aa29fc10 Unloading class 0x00000204c218a000 'java/lang/invoke/LambdaForm$DMH+0x00000204c218a000'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 4.623 Thread 0x00000205095ae2e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65efe10}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x00000000d65efe10) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.623 Thread 0x00000205095ae2e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65f6b50}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x00000000d65f6b50) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.624 Thread 0x00000205095ae2e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65fb3a0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x00000000d65fb3a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.626 Thread 0x00000205095ae2e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d661a410}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object)'> (0x00000000d661a410) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.051 Thread 0x00000205095ae2e0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d64694d0}: Found class java.lang.Object, but interface was expected> (0x00000000d64694d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 6.182 Thread 0x00000205095ae2e0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d64a8a88}: Found class java.lang.Object, but interface was expected> (0x00000000d64a8a88) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 6.451 Thread 0x0000020508e1ce80 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6530b40}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, long)'> (0x00000000d6530b40) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.467 Thread 0x00000205095b1df0 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'{0x00000000d6666240}> (0x00000000d6666240) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 625]
Event: 6.488 Thread 0x00000205095ac8a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d67d11e0}: 'int java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object)'> (0x00000000d67d11e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.530 Thread 0x00000205095ac8a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d697d1c8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x00000000d697d1c8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.532 Thread 0x00000205095ac8a0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d5580968}: Found class java.lang.Object, but interface was expected> (0x00000000d5580968) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 6.602 Thread 0x00000205095ac8a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5886418}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5886418) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.612 Thread 0x00000205095ac8a0 Exception <a 'java/io/FileNotFoundException'{0x00000000d58f4570}> (0x00000000d58f4570) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 6.612 Thread 0x00000205095ac8a0 Exception <a 'java/io/FileNotFoundException'{0x00000000d58f5568}> (0x00000000d58f5568) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 7.022 Thread 0x00000205095ac8a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d60b7d50}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d60b7d50) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 7.153 Thread 0x0000020508973d40 Exception <a 'java/io/FileNotFoundException'{0x00000000d5f9dcb0}> (0x00000000d5f9dcb0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 7.153 Thread 0x0000020508973d40 Exception <a 'java/io/FileNotFoundException'{0x00000000d5f9eca0}> (0x00000000d5f9eca0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 7.153 Thread 0x0000020508973d40 Exception <a 'java/io/FileNotFoundException'{0x00000000d5f9fa08}> (0x00000000d5f9fa08) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 7.202 Thread 0x00000205095ac8a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65e5008}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d65e5008) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 7.267 Thread 0x00000205095ac8a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5fb8570}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d5fb8570) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 6.850 Executing VM operation: ICBufferFull
Event: 6.850 Executing VM operation: ICBufferFull done
Event: 6.887 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 6.889 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 6.894 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 6.894 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 7.052 Executing VM operation: ICBufferFull
Event: 7.053 Executing VM operation: ICBufferFull done
Event: 7.081 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 7.084 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 7.209 Executing VM operation: ICBufferFull
Event: 7.209 Executing VM operation: ICBufferFull done
Event: 7.213 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 7.215 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 7.295 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 7.298 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 7.387 Executing VM operation: ICBufferFull
Event: 7.387 Executing VM operation: ICBufferFull done
Event: 7.463 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 7.466 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad7c2490
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad802d10
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad803190
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad805c10
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad834110
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad834a90
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad839410
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad855c90
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad864510
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad866e10
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad86f710
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad894490
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad898690
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad899010
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad8d1590
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad8d2010
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad8dbe10
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad8e0b10
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad8e1590
Event: 5.439 Thread 0x00000204aa29fc10 flushing  nmethod 0x00000204ad8ebd90

Events (20 events):
Event: 3.790 Thread 0x00000204aa1fefe0 Thread added: 0x0000020508e1c160
Event: 3.790 Thread 0x00000204aa1fefe0 Thread added: 0x0000020508e1b440
Event: 3.790 Thread 0x00000204aa1fefe0 Thread added: 0x0000020508e1adb0
Event: 3.790 Thread 0x00000204aa1fefe0 Thread added: 0x00000205095b0a40
Event: 3.790 Thread 0x00000204aa1fefe0 Thread added: 0x00000205095ac210
Event: 3.790 Thread 0x00000204aa1fefe0 Thread added: 0x00000205095acf30
Event: 3.790 Thread 0x00000204aa1fefe0 Thread added: 0x00000205095b1760
Event: 3.791 Thread 0x00000204aa1fefe0 Thread added: 0x00000205095ad5c0
Event: 3.825 Thread 0x00000204aa1fefe0 Thread added: 0x00000205095af000
Event: 4.057 Thread 0x0000020508c96830 Thread exited: 0x0000020508c96830
Event: 4.072 Thread 0x0000020508e1ce80 Thread added: 0x00000205095b03b0
Event: 4.105 Thread 0x00000204aa1fefe0 Thread added: 0x00000205095adc50
Event: 4.105 Thread 0x00000204aa1fefe0 Thread added: 0x00000205095ae2e0
Event: 4.111 Thread 0x0000020503042cb0 Thread added: 0x0000020508f79430
Event: 4.577 Thread 0x0000020503042cb0 Thread added: 0x0000020508fa4d70
Event: 5.382 Thread 0x0000020508fa4d70 Thread exited: 0x0000020508fa4d70
Event: 5.597 Thread 0x00000204aa2b5d20 Thread added: 0x0000020508716550
Event: 6.063 Thread 0x00000205095ae2e0 Thread added: 0x00000205095b1df0
Event: 6.185 Thread 0x00000205095ae2e0 Thread added: 0x00000205095ac8a0
Event: 7.375 Thread 0x00000205080bb3b0 Thread exited: 0x00000205080bb3b0


Dynamic libraries:
0x00007ff7195b0000 - 0x00007ff7195be000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.exe
0x00007ffb67ed0000 - 0x00007ffb680e7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb66ef0000 - 0x00007ffb66fb4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb656e0000 - 0x00007ffb65ab0000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb651d0000 - 0x00007ffb652e1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffb48380000 - 0x00007ffb48398000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jli.dll
0x00007ffb485e0000 - 0x00007ffb485fe000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffb66c10000 - 0x00007ffb66dc1000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb65b30000 - 0x00007ffb65b56000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb4af60000 - 0x00007ffb4b1fc000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe\COMCTL32.dll
0x00007ffb67e60000 - 0x00007ffb67e89000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb65070000 - 0x00007ffb65193000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb64fd0000 - 0x00007ffb6506a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb67c50000 - 0x00007ffb67cf7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb65b60000 - 0x00007ffb65b91000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffb4a670000 - 0x00007ffb4a67c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffb48010000 - 0x00007ffb4809d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\msvcp140.dll
0x00007ffaeac70000 - 0x00007ffaeba07000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server\jvm.dll
0x00007ffb66b50000 - 0x00007ffb66c01000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb66fc0000 - 0x00007ffb67068000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb651a0000 - 0x00007ffb651c8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffb66dd0000 - 0x00007ffb66ee8000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb67a30000 - 0x00007ffb67aa1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb63db0000 - 0x00007ffb63dfd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffb5af50000 - 0x00007ffb5af84000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb62690000 - 0x00007ffb6269a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb63d90000 - 0x00007ffb63da3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffb63ff0000 - 0x00007ffb64008000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffb4a4f0000 - 0x00007ffb4a4fa000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
0x00007ffb61d80000 - 0x00007ffb61fb3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb66690000 - 0x00007ffb66a21000 	C:\WINDOWS\System32\combase.dll
0x00007ffb67070000 - 0x00007ffb67148000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffb4f720000 - 0x00007ffb4f752000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb653b0000 - 0x00007ffb6542b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffb4a160000 - 0x00007ffb4a16f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
0x00007ffb48340000 - 0x00007ffb4835f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
0x00007ffb65ba0000 - 0x00007ffb66441000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffb65430000 - 0x00007ffb6556f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffb62e60000 - 0x00007ffb6377a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffb67400000 - 0x00007ffb6750a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffb66ae0000 - 0x00007ffb66b49000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffb64f00000 - 0x00007ffb64f25000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffb48320000 - 0x00007ffb48338000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
0x00007ffb4a0f0000 - 0x00007ffb4a100000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
0x00007ffb5e820000 - 0x00007ffb5e94c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffb64530000 - 0x00007ffb64599000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffb48240000 - 0x00007ffb48256000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
0x00007ffb495b0000 - 0x00007ffb495c0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
0x00007ffb49230000 - 0x00007ffb49274000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
0x00007ffb67ab0000 - 0x00007ffb67c50000 	C:\WINDOWS\System32\ole32.dll
0x00007ffb647c0000 - 0x00007ffb647db000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffb63fb0000 - 0x00007ffb63fe7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffb64640000 - 0x00007ffb64668000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffb647e0000 - 0x00007ffb647ec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffb63a50000 - 0x00007ffb63a7d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffb67160000 - 0x00007ffb67169000 	C:\WINDOWS\System32\NSI.dll
0x00007ffb1a5c0000 - 0x00007ffb1a609000 	C:\Users\<USER>\AppData\Local\Temp\jna-71916\jna13182268162738980999.dll
0x00007ffb673e0000 - 0x00007ffb673e8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffb5e9f0000 - 0x00007ffb5ea09000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffb5e600000 - 0x00007ffb5e61f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL

JVMTI agents:
c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar path:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll, loaded, initialized, instrumentlib options:none

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854;C:\Users\<USER>\AppData\Local\Temp\jna-71916

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java\ss_ws --pipe=\\.\pipe\lsp-7393c721c939eaeaff8fb765d5551878-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytesseract;C:\Program Files\Common Files\Oracle\Java\javapath;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\TortoiseSVN\bin;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Cloudflare\Cloudflare WARP\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Docker\Docker\resources\bin;D:\A\Git\Git\cmd;C:\Program Files\CMake\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;D:\A\Anaconda;D:\A\Anaconda\Library\mingw-w64\bin;D:\A\Anaconda\Library\usr\bin;D:\A\Anaconda\Library\bin;D:\A\Anaconda\Scripts;D:\A\Scripts\;D:\A\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin
USERNAME=HUY
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 0 days 5:27 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x3c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for the first 12 processors :
  Max Mhz: 2688, Current Mhz: 2688, Mhz Limit: 2688

Memory: 4k page, system-wide physical 16163M (2052M free)
TotalPageFile size 20259M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 281M, peak: 283M
current process commit charge ("private bytes"): 389M, peak: 391M

vm_info: OpenJDK 64-Bit Server VM (21.0.8+9-LTS) for windows-amd64 JRE (21.0.8+9-LTS), built on 2025-07-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
