#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1234576 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=7756, tid=12196
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.8+9 (21.0.8+9) (build 21.0.8+9-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.8+9 (21.0.8+9-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-3c6000a0380514e4c4c48e8b0db79aa0-sock

Host: 11th Gen Intel(R) Core(TM) i5-11400H @ 2.70GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Thu Sep 25 03:08:46 2025 SE Asia Standard Time elapsed time: 5.389181 seconds (0d 0h 0m 5s)

---------------  T H R E A D  ---------------

Current thread (0x000002956f68a440):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=12196, stack(0x000000aeb4f00000,0x000000aeb5000000) (1024K)]


Current CompileTask:
C2:5389 6069       4       java.lang.invoke.MemberName::getMethodOrFieldType (72 bytes)

Stack: [0x000000aeb4f00000,0x000000aeb5000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d2449]
V  [jvm.dll+0x8ae341]
V  [jvm.dll+0x8b08be]
V  [jvm.dll+0x8b0fa3]
V  [jvm.dll+0x280c96]
V  [jvm.dll+0xc581d]
V  [jvm.dll+0xc5d53]
V  [jvm.dll+0x3b9162]
V  [jvm.dll+0x385315]
V  [jvm.dll+0x38477a]
V  [jvm.dll+0x248ed0]
V  [jvm.dll+0x2484af]
V  [jvm.dll+0x1c89ee]
V  [jvm.dll+0x257d4d]
V  [jvm.dll+0x2562ea]
V  [jvm.dll+0x3f2d16]
V  [jvm.dll+0x857e6b]
V  [jvm.dll+0x6d0b0d]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002957716a520, length=54, elements={
0x00000295167c17a0, 0x00000295168633e0, 0x000002952d25bfb0, 0x0000029516864b20,
0x0000029516865930, 0x0000029516868460, 0x000002951686bd00, 0x000002956f68a440,
0x0000029516877e60, 0x000002951686ddd0, 0x000002951686a950, 0x000002951686c390,
0x000002951686d0b0, 0x000002951686ca20, 0x000002951686d740, 0x0000029570626080,
0x000002951686afe0, 0x000002951686b670, 0x00000295755d3a50, 0x00000295755d40e0,
0x00000295755d5490, 0x00000295755d8280, 0x00000295755d61b0, 0x00000295755d4770,
0x0000029575c70f70, 0x00000295755d7bf0, 0x00000295755db070, 0x00000295755d9630,
0x00000295755d4e00, 0x00000295755d6840, 0x00000295755d9cc0, 0x00000295755da350,
0x00000295755da9e0, 0x00000295755d7560, 0x00000295755d5b20, 0x00000295755d6ed0,
0x00000295755d8fa0, 0x00000295755d8910, 0x000002957842ef20, 0x0000029578430ff0,
0x000002957842a6f0, 0x000002957842baa0, 0x000002957842f5b0, 0x000002957842e890,
0x0000029578430960, 0x000002957842a060, 0x000002957842c7c0, 0x000002957842c130,
0x0000029578431680, 0x000002957842fc40, 0x000002957842ad80, 0x000002957842b410,
0x000002957842db70, 0x000002957842ce50
}

Java Threads: ( => current thread )
  0x00000295167c17a0 JavaThread "main"                              [_thread_blocked, id=8776, stack(0x000000aeb4500000,0x000000aeb4600000) (1024K)]
  0x00000295168633e0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=16432, stack(0x000000aeb4900000,0x000000aeb4a00000) (1024K)]
  0x000002952d25bfb0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=20148, stack(0x000000aeb4a00000,0x000000aeb4b00000) (1024K)]
  0x0000029516864b20 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=18476, stack(0x000000aeb4b00000,0x000000aeb4c00000) (1024K)]
  0x0000029516865930 JavaThread "Attach Listener"            daemon [_thread_blocked, id=7848, stack(0x000000aeb4c00000,0x000000aeb4d00000) (1024K)]
  0x0000029516868460 JavaThread "Service Thread"             daemon [_thread_blocked, id=2708, stack(0x000000aeb4d00000,0x000000aeb4e00000) (1024K)]
  0x000002951686bd00 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=22000, stack(0x000000aeb4e00000,0x000000aeb4f00000) (1024K)]
=>0x000002956f68a440 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=12196, stack(0x000000aeb4f00000,0x000000aeb5000000) (1024K)]
  0x0000029516877e60 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=21372, stack(0x000000aeb5000000,0x000000aeb5100000) (1024K)]
  0x000002951686ddd0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=15264, stack(0x000000aeb5100000,0x000000aeb5200000) (1024K)]
  0x000002951686a950 JavaThread "Notification Thread"        daemon [_thread_blocked, id=9120, stack(0x000000aeb5400000,0x000000aeb5500000) (1024K)]
  0x000002951686c390 JavaThread "Active Thread: Equinox Container: d5d112c0-d674-4abc-ad6b-8d77401819a3"        [_thread_blocked, id=12692, stack(0x000000aeb5b00000,0x000000aeb5c00000) (1024K)]
  0x000002951686d0b0 JavaThread "Refresh Thread: Equinox Container: d5d112c0-d674-4abc-ad6b-8d77401819a3" daemon [_thread_blocked, id=14868, stack(0x000000aeb5c00000,0x000000aeb5d00000) (1024K)]
  0x000002951686ca20 JavaThread "Framework Event Dispatcher: Equinox Container: d5d112c0-d674-4abc-ad6b-8d77401819a3" daemon [_thread_blocked, id=12108, stack(0x000000aeb5e00000,0x000000aeb5f00000) (1024K)]
  0x000002951686d740 JavaThread "Start Level: Equinox Container: d5d112c0-d674-4abc-ad6b-8d77401819a3" daemon [_thread_blocked, id=17936, stack(0x000000aeb5f00000,0x000000aeb6000000) (1024K)]
  0x0000029570626080 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=1308, stack(0x000000aeb5200000,0x000000aeb5300000) (1024K)]
  0x000002951686afe0 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=18532, stack(0x000000aeb6200000,0x000000aeb6300000) (1024K)]
  0x000002951686b670 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=4080, stack(0x000000aeb6300000,0x000000aeb6400000) (1024K)]
  0x00000295755d3a50 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=10412, stack(0x000000aeb6400000,0x000000aeb6500000) (1024K)]
  0x00000295755d40e0 JavaThread "Worker-JM"                         [_thread_blocked, id=15688, stack(0x000000aeb6500000,0x000000aeb6600000) (1024K)]
  0x00000295755d5490 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=17280, stack(0x000000aeb5300000,0x000000aeb5400000) (1024K)]
  0x00000295755d8280 JavaThread "Worker-0: Repository registry initialization"        [_thread_in_Java, id=6076, stack(0x000000aeb6600000,0x000000aeb6700000) (1024K)]
  0x00000295755d61b0 JavaThread "Worker-1"                          [_thread_blocked, id=6184, stack(0x000000aeb6700000,0x000000aeb6800000) (1024K)]
  0x00000295755d4770 JavaThread "Java indexing"              daemon [_thread_blocked, id=20432, stack(0x000000aeb6800000,0x000000aeb6900000) (1024K)]
  0x0000029575c70f70 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=19000, stack(0x000000aeb6900000,0x000000aeb6a00000) (1024K)]
  0x00000295755d7bf0 JavaThread "Worker-2"                          [_thread_blocked, id=15460, stack(0x000000aeb6a00000,0x000000aeb6b00000) (1024K)]
  0x00000295755db070 JavaThread "Worker-3"                          [_thread_blocked, id=12944, stack(0x000000aeb6b00000,0x000000aeb6c00000) (1024K)]
  0x00000295755d9630 JavaThread "Thread-2"                   daemon [_thread_in_native, id=6036, stack(0x000000aeb6c00000,0x000000aeb6d00000) (1024K)]
  0x00000295755d4e00 JavaThread "Thread-3"                   daemon [_thread_in_native, id=8556, stack(0x000000aeb6d00000,0x000000aeb6e00000) (1024K)]
  0x00000295755d6840 JavaThread "Thread-4"                   daemon [_thread_in_native, id=18904, stack(0x000000aeb6e00000,0x000000aeb6f00000) (1024K)]
  0x00000295755d9cc0 JavaThread "Thread-5"                   daemon [_thread_in_native, id=7332, stack(0x000000aeb6f00000,0x000000aeb7000000) (1024K)]
  0x00000295755da350 JavaThread "Thread-6"                   daemon [_thread_in_native, id=19536, stack(0x000000aeb7000000,0x000000aeb7100000) (1024K)]
  0x00000295755da9e0 JavaThread "Thread-7"                   daemon [_thread_in_native, id=11808, stack(0x000000aeb7100000,0x000000aeb7200000) (1024K)]
  0x00000295755d7560 JavaThread "Thread-8"                   daemon [_thread_in_native, id=20712, stack(0x000000aeb7200000,0x000000aeb7300000) (1024K)]
  0x00000295755d5b20 JavaThread "Thread-9"                   daemon [_thread_in_native, id=16304, stack(0x000000aeb7300000,0x000000aeb7400000) (1024K)]
  0x00000295755d6ed0 JavaThread "Thread-10"                  daemon [_thread_in_native, id=12476, stack(0x000000aeb7400000,0x000000aeb7500000) (1024K)]
  0x00000295755d8fa0 JavaThread "Thread-11"                  daemon [_thread_in_native, id=3984, stack(0x000000aeb7500000,0x000000aeb7600000) (1024K)]
  0x00000295755d8910 JavaThread "Thread-12"                  daemon [_thread_in_native, id=22536, stack(0x000000aeb7600000,0x000000aeb7700000) (1024K)]
  0x000002957842ef20 JavaThread "Thread-13"                  daemon [_thread_in_native, id=11468, stack(0x000000aeb7700000,0x000000aeb7800000) (1024K)]
  0x0000029578430ff0 JavaThread "Thread-14"                  daemon [_thread_in_native, id=18684, stack(0x000000aeb7800000,0x000000aeb7900000) (1024K)]
  0x000002957842a6f0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=18728, stack(0x000000aeb7900000,0x000000aeb7a00000) (1024K)]
  0x000002957842baa0 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=4680, stack(0x000000aeb7a00000,0x000000aeb7b00000) (1024K)]
  0x000002957842f5b0 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=19172, stack(0x000000aeb7b00000,0x000000aeb7c00000) (1024K)]
  0x000002957842e890 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=20916, stack(0x000000aeb7c00000,0x000000aeb7d00000) (1024K)]
  0x0000029578430960 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=21752, stack(0x000000aeb7d00000,0x000000aeb7e00000) (1024K)]
  0x000002957842a060 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=13696, stack(0x000000aeb7e00000,0x000000aeb7f00000) (1024K)]
  0x000002957842c7c0 JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_blocked, id=11084, stack(0x000000aeb7f00000,0x000000aeb8000000) (1024K)]
  0x000002957842c130 JavaThread "ForkJoinPool.commonPool-worker-5" daemon [_thread_blocked, id=8632, stack(0x000000aeb8000000,0x000000aeb8100000) (1024K)]
  0x0000029578431680 JavaThread "ForkJoinPool.commonPool-worker-6" daemon [_thread_blocked, id=20200, stack(0x000000aeb8100000,0x000000aeb8200000) (1024K)]
  0x000002957842fc40 JavaThread "ForkJoinPool.commonPool-worker-7" daemon [_thread_blocked, id=2348, stack(0x000000aeb8200000,0x000000aeb8300000) (1024K)]
  0x000002957842ad80 JavaThread "ForkJoinPool.commonPool-worker-8" daemon [_thread_blocked, id=13440, stack(0x000000aeb8300000,0x000000aeb8400000) (1024K)]
  0x000002957842b410 JavaThread "ForkJoinPool.commonPool-worker-9" daemon [_thread_blocked, id=10420, stack(0x000000aeb8400000,0x000000aeb8500000) (1024K)]
  0x000002957842db70 JavaThread "ForkJoinPool.commonPool-worker-10" daemon [_thread_blocked, id=23448, stack(0x000000aeb8500000,0x000000aeb8600000) (1024K)]
  0x000002957842ce50 JavaThread "ForkJoinPool.commonPool-worker-11" daemon [_thread_blocked, id=16884, stack(0x000000aeb8600000,0x000000aeb8700000) (1024K)]
Total: 54

Other Threads:
  0x000002951685e7d0 VMThread "VM Thread"                           [id=22668, stack(0x000000aeb4800000,0x000000aeb4900000) (1024K)]
  0x000002952d13c950 WatcherThread "VM Periodic Task Thread"        [id=2856, stack(0x000000aeb4700000,0x000000aeb4800000) (1024K)]
  0x00000295167dfab0 WorkerThread "GC Thread#0"                     [id=21416, stack(0x000000aeb4600000,0x000000aeb4700000) (1024K)]
  0x00000295707946d0 WorkerThread "GC Thread#1"                     [id=9344, stack(0x000000aeb5500000,0x000000aeb5600000) (1024K)]
  0x0000029570aa7160 WorkerThread "GC Thread#2"                     [id=18572, stack(0x000000aeb5600000,0x000000aeb5700000) (1024K)]
  0x0000029570aa7500 WorkerThread "GC Thread#3"                     [id=7208, stack(0x000000aeb5700000,0x000000aeb5800000) (1024K)]
  0x0000029570aa78a0 WorkerThread "GC Thread#4"                     [id=12732, stack(0x000000aeb5800000,0x000000aeb5900000) (1024K)]
  0x0000029570aa7c40 WorkerThread "GC Thread#5"                     [id=4972, stack(0x000000aeb5900000,0x000000aeb5a00000) (1024K)]
  0x0000029570aa7fe0 WorkerThread "GC Thread#6"                     [id=22240, stack(0x000000aeb5a00000,0x000000aeb5b00000) (1024K)]
  0x00000295752820e0 WorkerThread "GC Thread#7"                     [id=1856, stack(0x000000aeb5d00000,0x000000aeb5e00000) (1024K)]
  0x00000295750df160 WorkerThread "GC Thread#8"                     [id=18712, stack(0x000000aeb6000000,0x000000aeb6100000) (1024K)]
  0x00000295755f3b60 WorkerThread "GC Thread#9"                     [id=16512, stack(0x000000aeb6100000,0x000000aeb6200000) (1024K)]
Total: 12

Threads with active compile tasks:
C2 CompilerThread0  5407 6069       4       java.lang.invoke.MemberName::getMethodOrFieldType (72 bytes)
C2 CompilerThread1  5407 6009       4       java.lang.invoke.MethodType::makeImpl (109 bytes)
C2 CompilerThread2  5407 5767   !   4       org.eclipse.osgi.internal.loader.BundleLoader::findClass0 (491 bytes)
Total: 3

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002952e000000-0x000002952eba0000-0x000002952eba0000), size 12189696, SharedBaseAddress: 0x000002952e000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002952f000000-0x000002956f000000, reserved size: 1073741824
Narrow klass base: 0x000002952e000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 12 total, 12 available
 Memory: 16163M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 10

Heap:
 PSYoungGen      total 23552K, used 20247K [0x00000000d5580000, 0x00000000d7380000, 0x0000000100000000)
  eden space 22016K, 86% used [0x00000000d5580000,0x00000000d6825ea8,0x00000000d6b00000)
  from space 1536K, 75% used [0x00000000d7200000,0x00000000d7320000,0x00000000d7380000)
  to   space 1536K, 0% used [0x00000000d7080000,0x00000000d7080000,0x00000000d7200000)
 ParOldGen       total 68608K, used 35260K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 51% used [0x0000000080000000,0x000000008226f1c0,0x0000000084300000)
 Metaspace       used 44884K, committed 46144K, reserved 1114112K
  class space    used 4909K, committed 5504K, reserved 1048576K

Card table byte_map: [0x0000029516160000,0x0000029516570000] _byte_map_base: 0x0000029515d60000

Marking Bits: (ParMarkBitMap*) 0x00007ffaeb92a340
 Begin Bits: [0x0000029528c60000, 0x000002952ac60000)
 End Bits:   [0x000002952ac60000, 0x000002952cc60000)

Polling page: 0x0000029514790000

Metaspace:

Usage:
  Non-class:     39.04 MB used.
      Class:      4.79 MB used.
       Both:     43.83 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      39.69 MB ( 62%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       5.38 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      45.06 MB (  4%) committed. 

Chunk freelists:
   Non-Class:  7.47 MB
       Class:  10.64 MB
        Both:  18.11 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 58.88 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 10.
num_arena_births: 964.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 721.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 24.
num_chunks_taken_from_freelist: 2970.
num_chunk_merges: 11.
num_chunk_splits: 1867.
num_chunks_enlarged: 1096.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=2760Kb max_used=2760Kb free=117240Kb
 bounds [0x0000029521480000, 0x0000029521740000, 0x00000295289b0000]
CodeHeap 'profiled nmethods': size=120000Kb used=11325Kb max_used=11325Kb free=108674Kb
 bounds [0x00000295199b0000, 0x000002951a4c0000, 0x0000029520ee0000]
CodeHeap 'non-nmethods': size=5760Kb used=1423Kb max_used=1432Kb free=4336Kb
 bounds [0x0000029520ee0000, 0x0000029521150000, 0x0000029521480000]
CodeCache: size=245760Kb, used=15508Kb, max_used=15517Kb, free=230250Kb
 total_blobs=6240, nmethods=5576, adapters=568, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 5.375 Thread 0x0000029516877e60 6182       3       com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection::<init> (44 bytes)
Event: 5.375 Thread 0x0000029516877e60 nmethod 6182 0x000002951a4b9810 code [0x000002951a4b99c0, 0x000002951a4b9ce0]
Event: 5.375 Thread 0x0000029516877e60 6176       3       com.google.common.collect.AbstractMultimap$Values::contains (9 bytes)
Event: 5.375 Thread 0x0000029516877e60 nmethod 6176 0x000002951a4b9e10 code [0x000002951a4b9fc0, 0x000002951a4ba198]
Event: 5.375 Thread 0x0000029516877e60 6177       3       com.google.common.collect.LinkedHashMultimap::containsValue (6 bytes)
Event: 5.376 Thread 0x0000029516877e60 nmethod 6177 0x000002951a4ba290 code [0x000002951a4ba440, 0x000002951a4ba580]
Event: 5.376 Thread 0x0000029516877e60 6179       3       com.google.common.collect.Maps$ViewCachingAbstractMap::values (23 bytes)
Event: 5.376 Thread 0x0000029516877e60 nmethod 6179 0x000002951a4ba610 code [0x000002951a4ba7e0, 0x000002951a4bac58]
Event: 5.376 Thread 0x0000029516877e60 6180       3       com.google.common.collect.Maps$Values::iterator (18 bytes)
Event: 5.376 Thread 0x0000029516877e60 nmethod 6180 0x000002951a4bae10 code [0x000002951a4bb020, 0x000002951a4bb730]
Event: 5.376 Thread 0x0000029516877e60 6181       3       com.google.common.collect.LinkedHashMultimap$ValueEntry::getPredecessorInMultimap (11 bytes)
Event: 5.376 Thread 0x0000029516877e60 nmethod 6181 0x000002951a4bb990 code [0x000002951a4bbb40, 0x000002951a4bbda0]
Event: 5.376 Thread 0x0000029516877e60 6184       3       com.google.inject.internal.Scoping::isNoScope (16 bytes)
Event: 5.376 Thread 0x0000029516877e60 nmethod 6184 0x000002951a4bbe90 code [0x000002951a4bc040, 0x000002951a4bc260]
Event: 5.376 Thread 0x0000029516877e60 6185       3       com.google.inject.internal.Scoping$1::getScopeInstance (4 bytes)
Event: 5.376 Thread 0x0000029516877e60 nmethod 6185 0x000002951a4bc310 code [0x000002951a4bc4a0, 0x000002951a4bc5b8]
Event: 5.376 Thread 0x0000029516877e60 6186       3       com.google.inject.internal.InternalInjectorCreator::isEagerSingleton (62 bytes)
Event: 5.377 Thread 0x0000029516877e60 nmethod 6186 0x000002951a4bc690 code [0x000002951a4bc8c0, 0x000002951a4bd1b8]
Event: 5.377 Thread 0x0000029516877e60 6187       3       com.google.inject.internal.Scoping::isEagerSingleton (38 bytes)
Event: 5.377 Thread 0x0000029516877e60 nmethod 6187 0x000002951a4bd410 code [0x000002951a4bd5c0, 0x000002951a4bd830]

GC Heap History (20 events):
Event: 3.618 GC heap before
{Heap before GC invocations=11 (full 1):
 PSYoungGen      total 25600K, used 25098K [0x00000000d5580000, 0x00000000d7300000, 0x0000000100000000)
  eden space 23552K, 100% used [0x00000000d5580000,0x00000000d6c80000,0x00000000d6c80000)
  from space 2048K, 75% used [0x00000000d6e80000,0x00000000d7002948,0x00000000d7080000)
  to   space 2560K, 0% used [0x00000000d7080000,0x00000000d7080000,0x00000000d7300000)
 ParOldGen       total 68608K, used 12763K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 18% used [0x0000000080000000,0x0000000080c76f50,0x0000000084300000)
 Metaspace       used 28022K, committed 29056K, reserved 1114112K
  class space    used 2764K, committed 3200K, reserved 1048576K
}
Event: 3.621 GC heap after
{Heap after GC invocations=11 (full 1):
 PSYoungGen      total 25088K, used 2043K [0x00000000d5580000, 0x00000000d7280000, 0x0000000100000000)
  eden space 23040K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6c00000)
  from space 2048K, 99% used [0x00000000d7080000,0x00000000d727eda0,0x00000000d7280000)
  to   space 2560K, 0% used [0x00000000d6d80000,0x00000000d6d80000,0x00000000d7000000)
 ParOldGen       total 68608K, used 14498K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 21% used [0x0000000080000000,0x0000000080e28ac8,0x0000000084300000)
 Metaspace       used 28022K, committed 29056K, reserved 1114112K
  class space    used 2764K, committed 3200K, reserved 1048576K
}
Event: 3.878 GC heap before
{Heap before GC invocations=12 (full 1):
 PSYoungGen      total 25088K, used 25083K [0x00000000d5580000, 0x00000000d7280000, 0x0000000100000000)
  eden space 23040K, 100% used [0x00000000d5580000,0x00000000d6c00000,0x00000000d6c00000)
  from space 2048K, 99% used [0x00000000d7080000,0x00000000d727eda0,0x00000000d7280000)
  to   space 2560K, 0% used [0x00000000d6d80000,0x00000000d6d80000,0x00000000d7000000)
 ParOldGen       total 68608K, used 14498K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 21% used [0x0000000080000000,0x0000000080e28ac8,0x0000000084300000)
 Metaspace       used 31140K, committed 32256K, reserved 1114112K
  class space    used 3107K, committed 3584K, reserved 1048576K
}
Event: 3.880 GC heap after
{Heap after GC invocations=12 (full 1):
 PSYoungGen      total 25088K, used 2412K [0x00000000d5580000, 0x00000000d7100000, 0x0000000100000000)
  eden space 22528K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b80000)
  from space 2560K, 94% used [0x00000000d6d80000,0x00000000d6fdb2f0,0x00000000d7000000)
  to   space 1024K, 0% used [0x00000000d7000000,0x00000000d7000000,0x00000000d7100000)
 ParOldGen       total 68608K, used 16309K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 23% used [0x0000000080000000,0x0000000080fed7f8,0x0000000084300000)
 Metaspace       used 31140K, committed 32256K, reserved 1114112K
  class space    used 3107K, committed 3584K, reserved 1048576K
}
Event: 4.025 GC heap before
{Heap before GC invocations=13 (full 1):
 PSYoungGen      total 25088K, used 24940K [0x00000000d5580000, 0x00000000d7100000, 0x0000000100000000)
  eden space 22528K, 100% used [0x00000000d5580000,0x00000000d6b80000,0x00000000d6b80000)
  from space 2560K, 94% used [0x00000000d6d80000,0x00000000d6fdb2f0,0x00000000d7000000)
  to   space 1024K, 0% used [0x00000000d7000000,0x00000000d7000000,0x00000000d7100000)
 ParOldGen       total 68608K, used 16309K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 23% used [0x0000000080000000,0x0000000080fed7f8,0x0000000084300000)
 Metaspace       used 33790K, committed 34944K, reserved 1114112K
  class space    used 3451K, committed 3968K, reserved 1048576K
}
Event: 4.028 GC heap after
{Heap after GC invocations=13 (full 1):
 PSYoungGen      total 23040K, used 1024K [0x00000000d5580000, 0x00000000d7400000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 1024K, 100% used [0x00000000d7000000,0x00000000d7100000,0x00000000d7100000)
  to   space 4608K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6f80000)
 ParOldGen       total 68608K, used 19193K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 27% used [0x0000000080000000,0x00000000812be400,0x0000000084300000)
 Metaspace       used 33790K, committed 34944K, reserved 1114112K
  class space    used 3451K, committed 3968K, reserved 1048576K
}
Event: 4.087 GC heap before
{Heap before GC invocations=14 (full 1):
 PSYoungGen      total 23040K, used 9223K [0x00000000d5580000, 0x00000000d7400000, 0x0000000100000000)
  eden space 22016K, 37% used [0x00000000d5580000,0x00000000d5d81d88,0x00000000d6b00000)
  from space 1024K, 100% used [0x00000000d7000000,0x00000000d7100000,0x00000000d7100000)
  to   space 4608K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6f80000)
 ParOldGen       total 68608K, used 19193K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 27% used [0x0000000080000000,0x00000000812be400,0x0000000084300000)
 Metaspace       used 34692K, committed 35904K, reserved 1114112K
  class space    used 3554K, committed 4096K, reserved 1048576K
}
Event: 4.089 GC heap after
{Heap after GC invocations=14 (full 1):
 PSYoungGen      total 23040K, used 914K [0x00000000d5580000, 0x00000000d7080000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 1024K, 89% used [0x00000000d6b00000,0x00000000d6be4830,0x00000000d6c00000)
  to   space 2048K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7080000)
 ParOldGen       total 68608K, used 20157K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 29% used [0x0000000080000000,0x00000000813af510,0x0000000084300000)
 Metaspace       used 34692K, committed 35904K, reserved 1114112K
  class space    used 3554K, committed 4096K, reserved 1048576K
}
Event: 4.128 GC heap before
{Heap before GC invocations=15 (full 1):
 PSYoungGen      total 23040K, used 4810K [0x00000000d5580000, 0x00000000d7080000, 0x0000000100000000)
  eden space 22016K, 17% used [0x00000000d5580000,0x00000000d594e058,0x00000000d6b00000)
  from space 1024K, 89% used [0x00000000d6b00000,0x00000000d6be4830,0x00000000d6c00000)
  to   space 2048K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7080000)
 ParOldGen       total 68608K, used 20157K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 29% used [0x0000000080000000,0x00000000813af510,0x0000000084300000)
 Metaspace       used 35053K, committed 36160K, reserved 1114112K
  class space    used 3593K, committed 4096K, reserved 1048576K
}
Event: 4.129 GC heap after
{Heap after GC invocations=15 (full 1):
 PSYoungGen      total 23040K, used 640K [0x00000000d5580000, 0x00000000d6f80000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 1024K, 62% used [0x00000000d6e80000,0x00000000d6f20000,0x00000000d6f80000)
  to   space 1024K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6c00000)
 ParOldGen       total 68608K, used 20787K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 30% used [0x0000000080000000,0x000000008144cc98,0x0000000084300000)
 Metaspace       used 35053K, committed 36160K, reserved 1114112K
  class space    used 3593K, committed 4096K, reserved 1048576K
}
Event: 4.129 GC heap before
{Heap before GC invocations=16 (full 2):
 PSYoungGen      total 23040K, used 640K [0x00000000d5580000, 0x00000000d6f80000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 1024K, 62% used [0x00000000d6e80000,0x00000000d6f20000,0x00000000d6f80000)
  to   space 1024K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6c00000)
 ParOldGen       total 68608K, used 20787K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 30% used [0x0000000080000000,0x000000008144cc98,0x0000000084300000)
 Metaspace       used 35053K, committed 36160K, reserved 1114112K
  class space    used 3593K, committed 4096K, reserved 1048576K
}
Event: 4.153 GC heap after
{Heap after GC invocations=16 (full 2):
 PSYoungGen      total 23040K, used 0K [0x00000000d5580000, 0x00000000d6f80000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 1024K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d6f80000)
  to   space 1024K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6c00000)
 ParOldGen       total 68608K, used 20381K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 29% used [0x0000000080000000,0x00000000813e7470,0x0000000084300000)
 Metaspace       used 35053K, committed 36160K, reserved 1114112K
  class space    used 3593K, committed 4096K, reserved 1048576K
}
Event: 4.314 GC heap before
{Heap before GC invocations=17 (full 2):
 PSYoungGen      total 23040K, used 22016K [0x00000000d5580000, 0x00000000d6f80000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 1024K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d6f80000)
  to   space 1024K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6c00000)
 ParOldGen       total 68608K, used 20381K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 29% used [0x0000000080000000,0x00000000813e7470,0x0000000084300000)
 Metaspace       used 36698K, committed 37888K, reserved 1114112K
  class space    used 3798K, committed 4288K, reserved 1048576K
}
Event: 4.319 GC heap after
{Heap after GC invocations=17 (full 2):
 PSYoungGen      total 23040K, used 1024K [0x00000000d5580000, 0x00000000d7e00000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 1024K, 100% used [0x00000000d6b00000,0x00000000d6c00000,0x00000000d6c00000)
  to   space 9728K, 0% used [0x00000000d7480000,0x00000000d7480000,0x00000000d7e00000)
 ParOldGen       total 68608K, used 27584K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 40% used [0x0000000080000000,0x0000000081af0340,0x0000000084300000)
 Metaspace       used 36698K, committed 37888K, reserved 1114112K
  class space    used 3798K, committed 4288K, reserved 1048576K
}
Event: 4.528 GC heap before
{Heap before GC invocations=18 (full 2):
 PSYoungGen      total 23040K, used 23040K [0x00000000d5580000, 0x00000000d7e00000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 1024K, 100% used [0x00000000d6b00000,0x00000000d6c00000,0x00000000d6c00000)
  to   space 9728K, 0% used [0x00000000d7480000,0x00000000d7480000,0x00000000d7e00000)
 ParOldGen       total 68608K, used 27584K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 40% used [0x0000000080000000,0x0000000081af0340,0x0000000084300000)
 Metaspace       used 38523K, committed 39680K, reserved 1114112K
  class space    used 4067K, committed 4608K, reserved 1048576K
}
Event: 4.531 GC heap after
{Heap after GC invocations=18 (full 2):
 PSYoungGen      total 28672K, used 6481K [0x00000000d5580000, 0x00000000d7b00000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 6656K, 97% used [0x00000000d7480000,0x00000000d7ad4610,0x00000000d7b00000)
  to   space 7168K, 0% used [0x00000000d6d00000,0x00000000d6d00000,0x00000000d7400000)
 ParOldGen       total 68608K, used 28499K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 41% used [0x0000000080000000,0x0000000081bd4de0,0x0000000084300000)
 Metaspace       used 38523K, committed 39680K, reserved 1114112K
  class space    used 4067K, committed 4608K, reserved 1048576K
}
Event: 4.771 GC heap before
{Heap before GC invocations=19 (full 2):
 PSYoungGen      total 28672K, used 28497K [0x00000000d5580000, 0x00000000d7b00000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 6656K, 97% used [0x00000000d7480000,0x00000000d7ad4610,0x00000000d7b00000)
  to   space 7168K, 0% used [0x00000000d6d00000,0x00000000d6d00000,0x00000000d7400000)
 ParOldGen       total 68608K, used 28499K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 41% used [0x0000000080000000,0x0000000081bd4de0,0x0000000084300000)
 Metaspace       used 39920K, committed 41216K, reserved 1114112K
  class space    used 4252K, committed 4864K, reserved 1048576K
}
Event: 4.776 GC heap after
{Heap after GC invocations=19 (full 2):
 PSYoungGen      total 23040K, used 736K [0x00000000d5580000, 0x00000000d7500000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 1024K, 71% used [0x00000000d6d00000,0x00000000d6db8000,0x00000000d6e00000)
  to   space 3072K, 0% used [0x00000000d7200000,0x00000000d7200000,0x00000000d7500000)
 ParOldGen       total 68608K, used 34792K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 50% used [0x0000000080000000,0x00000000821fa1b0,0x0000000084300000)
 Metaspace       used 39920K, committed 41216K, reserved 1114112K
  class space    used 4252K, committed 4864K, reserved 1048576K
}
Event: 5.061 GC heap before
{Heap before GC invocations=20 (full 2):
 PSYoungGen      total 23040K, used 22752K [0x00000000d5580000, 0x00000000d7500000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 1024K, 71% used [0x00000000d6d00000,0x00000000d6db8000,0x00000000d6e00000)
  to   space 3072K, 0% used [0x00000000d7200000,0x00000000d7200000,0x00000000d7500000)
 ParOldGen       total 68608K, used 34792K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 50% used [0x0000000080000000,0x00000000821fa1b0,0x0000000084300000)
 Metaspace       used 42514K, committed 43776K, reserved 1114112K
  class space    used 4585K, committed 5184K, reserved 1048576K
}
Event: 5.063 GC heap after
{Heap after GC invocations=20 (full 2):
 PSYoungGen      total 23552K, used 1152K [0x00000000d5580000, 0x00000000d7380000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 1536K, 75% used [0x00000000d7200000,0x00000000d7320000,0x00000000d7380000)
  to   space 1536K, 0% used [0x00000000d7080000,0x00000000d7080000,0x00000000d7200000)
 ParOldGen       total 68608K, used 35260K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 51% used [0x0000000080000000,0x000000008226f1c0,0x0000000084300000)
 Metaspace       used 42514K, committed 43776K, reserved 1114112K
  class space    used 4585K, committed 5184K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.016 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
Event: 0.080 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.098 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
Event: 0.103 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
Event: 0.106 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
Event: 0.109 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.129 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
Event: 0.192 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
Event: 1.182 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
Event: 2.573 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-71916\jna5301532726336050591.dll

Deoptimization events (20 events):
Event: 5.366 Thread 0x00000295755d8280 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000029521728648 relative=0x00000000000003e8
Event: 5.366 Thread 0x00000295755d8280 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000029521728648 method=java.util.HashMap.removeNode(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/util/HashMap$Node; @ 283 c2
Event: 5.366 Thread 0x00000295755d8280 DEOPT PACKING pc=0x0000029521728648 sp=0x000000aeb66fe5c0
Event: 5.366 Thread 0x00000295755d8280 DEOPT UNPACKING pc=0x0000029520f36da2 sp=0x000000aeb66fe4c8 mode 2
Event: 5.369 Thread 0x00000295755d8280 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000029521728648 relative=0x00000000000003e8
Event: 5.369 Thread 0x00000295755d8280 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000029521728648 method=java.util.HashMap.removeNode(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/util/HashMap$Node; @ 283 c2
Event: 5.369 Thread 0x00000295755d8280 DEOPT PACKING pc=0x0000029521728648 sp=0x000000aeb66fe5c0
Event: 5.369 Thread 0x00000295755d8280 DEOPT UNPACKING pc=0x0000029520f36da2 sp=0x000000aeb66fe4c8 mode 2
Event: 5.369 Thread 0x00000295755d8280 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000029521728648 relative=0x00000000000003e8
Event: 5.369 Thread 0x00000295755d8280 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000029521728648 method=java.util.HashMap.removeNode(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/util/HashMap$Node; @ 283 c2
Event: 5.369 Thread 0x00000295755d8280 DEOPT PACKING pc=0x0000029521728648 sp=0x000000aeb66fe5c0
Event: 5.369 Thread 0x00000295755d8280 DEOPT UNPACKING pc=0x0000029520f36da2 sp=0x000000aeb66fe4c8 mode 2
Event: 5.369 Thread 0x00000295755d8280 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000029521728648 relative=0x00000000000003e8
Event: 5.369 Thread 0x00000295755d8280 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000029521728648 method=java.util.HashMap.removeNode(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/util/HashMap$Node; @ 283 c2
Event: 5.369 Thread 0x00000295755d8280 DEOPT PACKING pc=0x0000029521728648 sp=0x000000aeb66fe5c0
Event: 5.369 Thread 0x00000295755d8280 DEOPT UNPACKING pc=0x0000029520f36da2 sp=0x000000aeb66fe4c8 mode 2
Event: 5.369 Thread 0x00000295755d8280 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000295217249fc relative=0x000000000000031c
Event: 5.369 Thread 0x00000295755d8280 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000295217249fc method=java.util.HashMap.removeNode(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/util/HashMap$Node; @ 283 c2
Event: 5.369 Thread 0x00000295755d8280 DEOPT PACKING pc=0x00000295217249fc sp=0x000000aeb66fe520
Event: 5.369 Thread 0x00000295755d8280 DEOPT UNPACKING pc=0x0000029520f36da2 sp=0x000000aeb66fe4c8 mode 2

Classes loaded (20 events):
Event: 4.526 Loading class java/lang/foreign/MemorySegment
Event: 4.527 Loading class java/lang/foreign/MemorySegment done
Event: 4.786 Loading class sun/reflect/annotation/AnnotatedTypeFactory$AnnotatedParameterizedTypeImpl
Event: 4.786 Loading class java/lang/reflect/AnnotatedParameterizedType
Event: 4.786 Loading class java/lang/reflect/AnnotatedParameterizedType done
Event: 4.786 Loading class sun/reflect/annotation/AnnotatedTypeFactory$AnnotatedParameterizedTypeImpl done
Event: 4.825 Loading class java/util/concurrent/CompletionService
Event: 4.825 Loading class java/util/concurrent/CompletionService done
Event: 4.855 Loading class sun/reflect/generics/scope/ConstructorScope
Event: 4.855 Loading class sun/reflect/generics/scope/ConstructorScope done
Event: 4.855 Loading class sun/reflect/generics/tree/VoidDescriptor
Event: 4.855 Loading class sun/reflect/generics/tree/VoidDescriptor done
Event: 4.977 Loading class java/nio/channels/ServerSocketChannel
Event: 4.977 Loading class java/nio/channels/ServerSocketChannel done
Event: 5.102 Loading class java/lang/ExceptionInInitializerError
Event: 5.103 Loading class java/lang/ExceptionInInitializerError done
Event: 5.103 Loading class sun/misc/Unsafe
Event: 5.104 Loading class sun/misc/Unsafe done
Event: 5.133 Loading class jdk/internal/org/objectweb/asm/ClassReader
Event: 5.134 Loading class jdk/internal/org/objectweb/asm/ClassReader done

Classes unloaded (7 events):
Event: 3.096 Thread 0x000002951685e7d0 Unloading class 0x000002952f1ac800 'java/lang/invoke/LambdaForm$MH+0x000002952f1ac800'
Event: 3.096 Thread 0x000002951685e7d0 Unloading class 0x000002952f1ac400 'java/lang/invoke/LambdaForm$MH+0x000002952f1ac400'
Event: 3.096 Thread 0x000002951685e7d0 Unloading class 0x000002952f1ac000 'java/lang/invoke/LambdaForm$MH+0x000002952f1ac000'
Event: 3.096 Thread 0x000002951685e7d0 Unloading class 0x000002952f1abc00 'java/lang/invoke/LambdaForm$MH+0x000002952f1abc00'
Event: 3.096 Thread 0x000002951685e7d0 Unloading class 0x000002952f1ab800 'java/lang/invoke/LambdaForm$BMH+0x000002952f1ab800'
Event: 3.096 Thread 0x000002951685e7d0 Unloading class 0x000002952f1ab400 'java/lang/invoke/LambdaForm$DMH+0x000002952f1ab400'
Event: 3.096 Thread 0x000002951685e7d0 Unloading class 0x000002952f1aa000 'java/lang/invoke/LambdaForm$DMH+0x000002952f1aa000'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 4.160 Thread 0x00000295755d61b0 Exception <a 'java/io/FileNotFoundException'{0x00000000d564e0b8}> (0x00000000d564e0b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 4.162 Thread 0x00000295755d61b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d569b518}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d569b518) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.162 Thread 0x00000295755d61b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d569f1d0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d569f1d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.162 Thread 0x00000295755d61b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d56be528}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d56be528) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.164 Thread 0x00000295755d61b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d56ca3a8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d56ca3a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.261 Thread 0x00000295755d8280 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5e52c90}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5e52c90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.268 Thread 0x00000295755d61b0 Exception <a 'java/io/FileNotFoundException'{0x00000000d5ef37a0}> (0x00000000d5ef37a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 4.269 Thread 0x00000295755d61b0 Exception <a 'java/io/FileNotFoundException'{0x00000000d5ef44d8}> (0x00000000d5ef44d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 4.350 Thread 0x000002957842f5b0 Exception <a 'java/io/FileNotFoundException'{0x00000000d5d79f80}> (0x00000000d5d79f80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 4.386 Thread 0x000002957842f5b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d609d238}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d609d238) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.386 Thread 0x000002957842f5b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d60c75a8}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d60c75a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.387 Thread 0x000002957842f5b0 Implicit null exception at 0x0000029521674d76 to 0x0000029521675098
Event: 4.391 Thread 0x000002957842f5b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6120cf8}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d6120cf8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.395 Thread 0x00000295755d7bf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d61d1ae0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d61d1ae0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.488 Thread 0x00000295755d8280 Exception <a 'java/lang/NoClassDefFoundError'{0x00000000d678f8a8}: javax/enterprise/inject/Typed> (0x00000000d678f8a8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 301]
Event: 5.105 Thread 0x00000295755d8280 Exception <a 'java/lang/ExceptionInInitializerError'{0x00000000d5945ff8}> (0x00000000d5945ff8) 
thrown [s\src\hotspot\share\oops\instanceKlass.cpp, line 1220]
Event: 5.130 Thread 0x00000295755d8280 Implicit null exception at 0x000002952153f903 to 0x000002952153fb84
Event: 5.135 Thread 0x00000295755d8280 Implicit null exception at 0x00000295215cb1fe to 0x00000295215cb470
Event: 5.135 Thread 0x00000295755d8280 Implicit null exception at 0x000002952153589c to 0x00000295215367bc
Event: 5.137 Thread 0x00000295755d8280 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5afa828}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, int, java.lang.Object)'> (0x00000000d5afa828) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 4.238 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 4.238 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 4.314 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 4.319 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 4.334 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 4.334 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 4.475 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 4.475 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 4.528 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 4.531 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 4.771 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 4.776 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 4.832 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 4.832 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 4.948 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 4.948 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.061 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 5.063 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 5.178 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.178 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x0000029519ff8010
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x0000029519ffb790
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a01be10
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a024290
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a025d90
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a026690
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a028490
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a029d10
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a02a690
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a02b790
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a02bc10
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a02c910
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a02e310
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a078310
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a078610
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a0bbc10
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a11d090
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a132990
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a149f90
Event: 4.142 Thread 0x000002951685e7d0 flushing  nmethod 0x000002951a14c290

Events (20 events):
Event: 3.902 Thread 0x00000295167c17a0 Thread added: 0x00000295755d5b20
Event: 3.902 Thread 0x00000295167c17a0 Thread added: 0x00000295755d6ed0
Event: 3.903 Thread 0x00000295167c17a0 Thread added: 0x00000295755d8fa0
Event: 3.903 Thread 0x00000295167c17a0 Thread added: 0x00000295755d8910
Event: 3.903 Thread 0x00000295167c17a0 Thread added: 0x000002957842ef20
Event: 3.903 Thread 0x00000295167c17a0 Thread added: 0x0000029578430ff0
Event: 3.932 Thread 0x00000295167c17a0 Thread added: 0x000002957842a6f0
Event: 4.100 Thread 0x00000295167c17a0 Thread added: 0x000002957842baa0
Event: 4.100 Thread 0x00000295167c17a0 Thread added: 0x000002957842f5b0
Event: 4.271 Thread 0x00000295755d61b0 Thread added: 0x000002957842e890
Event: 4.272 Thread 0x00000295755d61b0 Thread added: 0x0000029578430960
Event: 4.272 Thread 0x000002957842e890 Thread added: 0x000002957842a060
Event: 4.273 Thread 0x0000029578430960 Thread added: 0x000002957842c7c0
Event: 4.273 Thread 0x000002957842a060 Thread added: 0x000002957842c130
Event: 4.274 Thread 0x000002957842c7c0 Thread added: 0x0000029578431680
Event: 4.275 Thread 0x000002957842c130 Thread added: 0x000002957842fc40
Event: 4.281 Thread 0x0000029578431680 Thread added: 0x000002957842ad80
Event: 4.286 Thread 0x000002957842fc40 Thread added: 0x000002957842b410
Event: 4.288 Thread 0x000002957842ad80 Thread added: 0x000002957842db70
Event: 4.294 Thread 0x000002957842b410 Thread added: 0x000002957842ce50


Dynamic libraries:
0x00007ff7195b0000 - 0x00007ff7195be000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.exe
0x00007ffb67ed0000 - 0x00007ffb680e7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb66ef0000 - 0x00007ffb66fb4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb656e0000 - 0x00007ffb65ab0000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb651d0000 - 0x00007ffb652e1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffb48380000 - 0x00007ffb48398000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jli.dll
0x00007ffb485e0000 - 0x00007ffb485fe000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffb66c10000 - 0x00007ffb66dc1000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb65b30000 - 0x00007ffb65b56000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb4af60000 - 0x00007ffb4b1fc000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe\COMCTL32.dll
0x00007ffb67e60000 - 0x00007ffb67e89000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb67c50000 - 0x00007ffb67cf7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb65070000 - 0x00007ffb65193000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb64fd0000 - 0x00007ffb6506a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb65b60000 - 0x00007ffb65b91000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffb4a670000 - 0x00007ffb4a67c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffb48010000 - 0x00007ffb4809d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\msvcp140.dll
0x00007ffaeac70000 - 0x00007ffaeba07000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server\jvm.dll
0x00007ffb66b50000 - 0x00007ffb66c01000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb66fc0000 - 0x00007ffb67068000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb651a0000 - 0x00007ffb651c8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffb66dd0000 - 0x00007ffb66ee8000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb67a30000 - 0x00007ffb67aa1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb63db0000 - 0x00007ffb63dfd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffb62690000 - 0x00007ffb6269a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb5af50000 - 0x00007ffb5af84000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb63d90000 - 0x00007ffb63da3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffb63ff0000 - 0x00007ffb64008000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffb4a4f0000 - 0x00007ffb4a4fa000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
0x00007ffb61d80000 - 0x00007ffb61fb3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb66690000 - 0x00007ffb66a21000 	C:\WINDOWS\System32\combase.dll
0x00007ffb67070000 - 0x00007ffb67148000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffb4f720000 - 0x00007ffb4f752000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb653b0000 - 0x00007ffb6542b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffb4a160000 - 0x00007ffb4a16f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
0x00007ffb48340000 - 0x00007ffb4835f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
0x00007ffb65ba0000 - 0x00007ffb66441000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffb65430000 - 0x00007ffb6556f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffb62e60000 - 0x00007ffb6377a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffb67400000 - 0x00007ffb6750a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffb66ae0000 - 0x00007ffb66b49000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffb64f00000 - 0x00007ffb64f25000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffb48320000 - 0x00007ffb48338000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
0x00007ffb4a0f0000 - 0x00007ffb4a100000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
0x00007ffb5e820000 - 0x00007ffb5e94c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffb64530000 - 0x00007ffb64599000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffb48240000 - 0x00007ffb48256000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
0x00007ffb495b0000 - 0x00007ffb495c0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
0x00007ffb483e0000 - 0x00007ffb48424000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
0x00007ffb67ab0000 - 0x00007ffb67c50000 	C:\WINDOWS\System32\ole32.dll
0x00007ffb647c0000 - 0x00007ffb647db000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffb63fb0000 - 0x00007ffb63fe7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffb64640000 - 0x00007ffb64668000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffb647e0000 - 0x00007ffb647ec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffb63a50000 - 0x00007ffb63a7d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffb67160000 - 0x00007ffb67169000 	C:\WINDOWS\System32\NSI.dll
0x00007ffb13020000 - 0x00007ffb13069000 	C:\Users\<USER>\AppData\Local\Temp\jna-71916\jna5301532726336050591.dll
0x00007ffb673e0000 - 0x00007ffb673e8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffb5e9f0000 - 0x00007ffb5ea09000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffb5e600000 - 0x00007ffb5e61f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL

JVMTI agents:
c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar path:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll, loaded, initialized, instrumentlib options:none

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854;C:\Users\<USER>\AppData\Local\Temp\jna-71916

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-3c6000a0380514e4c4c48e8b0db79aa0-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytesseract;C:\Program Files\Common Files\Oracle\Java\javapath;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\TortoiseSVN\bin;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Cloudflare\Cloudflare WARP\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Docker\Docker\resources\bin;D:\A\Git\Git\cmd;C:\Program Files\CMake\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;D:\A\Anaconda;D:\A\Anaconda\Library\mingw-w64\bin;D:\A\Anaconda\Library\usr\bin;D:\A\Anaconda\Library\bin;D:\A\Anaconda\Scripts;D:\A\Scripts\;D:\A\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin
USERNAME=HUY
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 0 days 5:27 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x3c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for the first 12 processors :
  Max Mhz: 2688, Current Mhz: 2688, Mhz Limit: 2688

Memory: 4k page, system-wide physical 16163M (1988M free)
TotalPageFile size 20259M (AvailPageFile size 12M)
current process WorkingSet (physical memory assigned to process): 232M, peak: 240M
current process commit charge ("private bytes"): 349M, peak: 359M

vm_info: OpenJDK 64-Bit Server VM (21.0.8+9-LTS) for windows-amd64 JRE (21.0.8+9-LTS), built on 2025-07-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
