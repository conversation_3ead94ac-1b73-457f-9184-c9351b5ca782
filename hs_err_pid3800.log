#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 889616 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=3800, tid=5964
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.8+9 (21.0.8+9) (build 21.0.8+9-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.8+9 (21.0.8+9-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java\ss_ws --pipe=\\.\pipe\lsp-41b511739beb21790d8d2cdf6ddf0c09-sock

Host: 11th Gen Intel(R) Core(TM) i5-11400H @ 2.70GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Thu Sep 25 03:08:36 2025 SE Asia Standard Time elapsed time: 6.652803 seconds (0d 0h 0m 6s)

---------------  T H R E A D  ---------------

Current thread (0x0000013740be5850):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=5964, stack(0x0000000844000000,0x0000000844100000) (1024K)]


Current CompileTask:
C2:6652 6243       4       lombok.core.FieldAugment::set (8 bytes)

Stack: [0x0000000844000000,0x0000000844100000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d2449]
V  [jvm.dll+0x8ae341]
V  [jvm.dll+0x8b08be]
V  [jvm.dll+0x8b0fa3]
V  [jvm.dll+0x280c96]
V  [jvm.dll+0xc581d]
V  [jvm.dll+0xc5d53]
V  [jvm.dll+0x2f539d]
V  [jvm.dll+0x5fa00a]
V  [jvm.dll+0x252092]
V  [jvm.dll+0x24a85c]
V  [jvm.dll+0x2483a4]
V  [jvm.dll+0x1c89ee]
V  [jvm.dll+0x257d4d]
V  [jvm.dll+0x2562ea]
V  [jvm.dll+0x3f2d16]
V  [jvm.dll+0x857e6b]
V  [jvm.dll+0x6d0b0d]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000137a0532810, length=42, elements={
0x0000013740b34bf0, 0x0000013740bcef90, 0x0000013740bd0980, 0x0000013740bd4d30,
0x0000013740bd5d30, 0x0000013740bd8700, 0x0000013740bdf940, 0x0000013740be5850,
0x0000013740be9040, 0x000001379996ace0, 0x000001379996cdb0, 0x000001379996d440,
0x000001379996b370, 0x000001379996dad0, 0x000001379996e160, 0x000001379996ba00,
0x000001379996c090, 0x000001379996c720, 0x000001379fbca1e0, 0x000001379fc4e2d0,
0x000001379fc50a30, 0x000001379fc4f680, 0x000001379fc4e960, 0x000001379fc4cf20,
0x000001379fc4fd10, 0x000001379fc51750, 0x000001379fc4a7c0, 0x000001379fc4ae50,
0x000001379fc503a0, 0x000001379fc51de0, 0x000001379fc4bb70, 0x000001379fc510c0,
0x000001379fc4b4e0, 0x000001379fc4c200, 0x000001379fc4eff0, 0x000001379fc4c890,
0x000001379fc4d5b0, 0x000001379fc4dc40, 0x00000137a0783860, 0x00000137a07831d0,
0x00000137a0781100, 0x000001379fbc86a0
}

Java Threads: ( => current thread )
  0x0000013740b34bf0 JavaThread "main"                              [_thread_blocked, id=20336, stack(0x0000000843600000,0x0000000843700000) (1024K)]
  0x0000013740bcef90 JavaThread "Reference Handler"          daemon [_thread_blocked, id=18224, stack(0x0000000843a00000,0x0000000843b00000) (1024K)]
  0x0000013740bd0980 JavaThread "Finalizer"                  daemon [_thread_blocked, id=21656, stack(0x0000000843b00000,0x0000000843c00000) (1024K)]
  0x0000013740bd4d30 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=11076, stack(0x0000000843c00000,0x0000000843d00000) (1024K)]
  0x0000013740bd5d30 JavaThread "Attach Listener"            daemon [_thread_blocked, id=13952, stack(0x0000000843d00000,0x0000000843e00000) (1024K)]
  0x0000013740bd8700 JavaThread "Service Thread"             daemon [_thread_blocked, id=22884, stack(0x0000000843e00000,0x0000000843f00000) (1024K)]
  0x0000013740bdf940 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=20568, stack(0x0000000843f00000,0x0000000844000000) (1024K)]
=>0x0000013740be5850 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=5964, stack(0x0000000844000000,0x0000000844100000) (1024K)]
  0x0000013740be9040 JavaThread "C1 CompilerThread0"         daemon [_thread_in_native, id=14428, stack(0x0000000844100000,0x0000000844200000) (1024K)]
  0x000001379996ace0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=20532, stack(0x0000000844200000,0x0000000844300000) (1024K)]
  0x000001379996cdb0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=3988, stack(0x0000000844400000,0x0000000844500000) (1024K)]
  0x000001379996d440 JavaThread "Active Thread: Equinox Container: f2839bb0-0cb3-4f49-bcad-c2b24750b1c0"        [_thread_blocked, id=19412, stack(0x0000000844c00000,0x0000000844d00000) (1024K)]
  0x000001379996b370 JavaThread "Framework Event Dispatcher: Equinox Container: f2839bb0-0cb3-4f49-bcad-c2b24750b1c0" daemon [_thread_blocked, id=16140, stack(0x0000000844d00000,0x0000000844e00000) (1024K)]
  0x000001379996dad0 JavaThread "Start Level: Equinox Container: f2839bb0-0cb3-4f49-bcad-c2b24750b1c0" daemon [_thread_blocked, id=15816, stack(0x0000000844e00000,0x0000000844f00000) (1024K)]
  0x000001379996e160 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=15836, stack(0x0000000845000000,0x0000000845100000) (1024K)]
  0x000001379996ba00 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=11140, stack(0x0000000845100000,0x0000000845200000) (1024K)]
  0x000001379996c090 JavaThread "Worker-JM"                         [_thread_blocked, id=22872, stack(0x0000000845200000,0x0000000845300000) (1024K)]
  0x000001379996c720 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=20596, stack(0x0000000844300000,0x0000000844400000) (1024K)]
  0x000001379fbca1e0 JavaThread "C2 CompilerThread1"         daemon [_thread_in_vm, id=21756, stack(0x0000000844500000,0x0000000844600000) (1024K)]
  0x000001379fc4e2d0 JavaThread "Worker-0"                          [_thread_blocked, id=11508, stack(0x0000000845500000,0x0000000845600000) (1024K)]
  0x000001379fc50a30 JavaThread "Worker-1"                          [_thread_blocked, id=20004, stack(0x0000000845600000,0x0000000845700000) (1024K)]
  0x000001379fc4f680 JavaThread "Thread-2"                   daemon [_thread_in_native, id=20256, stack(0x0000000845800000,0x0000000845900000) (1024K)]
  0x000001379fc4e960 JavaThread "Thread-3"                   daemon [_thread_in_native, id=7520, stack(0x0000000845900000,0x0000000845a00000) (1024K)]
  0x000001379fc4cf20 JavaThread "Thread-4"                   daemon [_thread_in_native, id=5300, stack(0x0000000845a00000,0x0000000845b00000) (1024K)]
  0x000001379fc4fd10 JavaThread "Thread-5"                   daemon [_thread_in_native, id=19136, stack(0x0000000845b00000,0x0000000845c00000) (1024K)]
  0x000001379fc51750 JavaThread "Thread-6"                   daemon [_thread_in_native, id=18172, stack(0x0000000845c00000,0x0000000845d00000) (1024K)]
  0x000001379fc4a7c0 JavaThread "Thread-7"                   daemon [_thread_in_native, id=11704, stack(0x0000000845d00000,0x0000000845e00000) (1024K)]
  0x000001379fc4ae50 JavaThread "Thread-8"                   daemon [_thread_in_native, id=20744, stack(0x0000000845e00000,0x0000000845f00000) (1024K)]
  0x000001379fc503a0 JavaThread "Thread-9"                   daemon [_thread_in_native, id=19824, stack(0x0000000845f00000,0x0000000846000000) (1024K)]
  0x000001379fc51de0 JavaThread "Thread-10"                  daemon [_thread_in_native, id=15856, stack(0x0000000846000000,0x0000000846100000) (1024K)]
  0x000001379fc4bb70 JavaThread "Thread-11"                  daemon [_thread_in_native, id=16516, stack(0x0000000846100000,0x0000000846200000) (1024K)]
  0x000001379fc510c0 JavaThread "Thread-12"                  daemon [_thread_in_native, id=20032, stack(0x0000000846200000,0x0000000846300000) (1024K)]
  0x000001379fc4b4e0 JavaThread "Thread-13"                  daemon [_thread_in_native, id=16396, stack(0x0000000846300000,0x0000000846400000) (1024K)]
  0x000001379fc4c200 JavaThread "Thread-14"                  daemon [_thread_in_native, id=20140, stack(0x0000000846400000,0x0000000846500000) (1024K)]
  0x000001379fc4eff0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=21628, stack(0x0000000846500000,0x0000000846600000) (1024K)]
  0x000001379fc4c890 JavaThread "Worker-2: Publish Diagnostics"        [_thread_in_vm, id=21572, stack(0x0000000846600000,0x0000000846700000) (1024K)]
  0x000001379fc4d5b0 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=16648, stack(0x0000000846700000,0x0000000846800000) (1024K)]
  0x000001379fc4dc40 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=6364, stack(0x0000000846800000,0x0000000846900000) (1024K)]
  0x00000137a0783860 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=3492, stack(0x0000000846900000,0x0000000846a00000) (1024K)]
  0x00000137a07831d0 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_in_Java, id=5960, stack(0x0000000846a00000,0x0000000846b00000) (1024K)]
  0x00000137a0781100 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=8256, stack(0x0000000846b00000,0x0000000846c00000) (1024K)]
  0x000001379fbc86a0 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=18948, stack(0x0000000845700000,0x0000000845800000) (1024K)]
Total: 42

Other Threads:
  0x0000013740bce770 VMThread "VM Thread"                           [id=6396, stack(0x0000000843900000,0x0000000843a00000) (1024K)]
  0x0000013740b9fb10 WatcherThread "VM Periodic Task Thread"        [id=19696, stack(0x0000000843800000,0x0000000843900000) (1024K)]
  0x0000013740b533f0 WorkerThread "GC Thread#0"                     [id=14800, stack(0x0000000843700000,0x0000000843800000) (1024K)]
  0x000001379f4a0900 WorkerThread "GC Thread#1"                     [id=19976, stack(0x0000000844600000,0x0000000844700000) (1024K)]
  0x000001379f14df60 WorkerThread "GC Thread#2"                     [id=21960, stack(0x0000000844700000,0x0000000844800000) (1024K)]
  0x000001379f5d1bd0 WorkerThread "GC Thread#3"                     [id=22648, stack(0x0000000844800000,0x0000000844900000) (1024K)]
  0x000001379f5d1f70 WorkerThread "GC Thread#4"                     [id=1104, stack(0x0000000844900000,0x0000000844a00000) (1024K)]
  0x000001379f384ff0 WorkerThread "GC Thread#5"                     [id=9152, stack(0x0000000844a00000,0x0000000844b00000) (1024K)]
  0x000001379f385390 WorkerThread "GC Thread#6"                     [id=9928, stack(0x0000000844b00000,0x0000000844c00000) (1024K)]
  0x000001379fa567e0 WorkerThread "GC Thread#7"                     [id=14828, stack(0x0000000844f00000,0x0000000845000000) (1024K)]
  0x000001379f9b0d10 WorkerThread "GC Thread#8"                     [id=2716, stack(0x0000000845300000,0x0000000845400000) (1024K)]
  0x000001379fc9b080 WorkerThread "GC Thread#9"                     [id=15356, stack(0x0000000845400000,0x0000000845500000) (1024K)]
Total: 12

Threads with active compile tasks:
C2 CompilerThread0  6672 6243       4       lombok.core.FieldAugment::set (8 bytes)
C1 CompilerThread0  6700 6387       3       org.eclipse.jdt.internal.compiler.lookup.Scope::findMemberType (723 bytes)
C2 CompilerThread1  6700 6049       4       org.eclipse.jdt.internal.compiler.lookup.BinaryTypeBinding::createMethod (1666 bytes)
C2 CompilerThread2  6700 6245   !   4       lombok.core.FieldAugment$ReflectionFieldAugment::getAndSet (80 bytes)
Total: 4

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffaeb923fd8] MethodCompileQueue_lock - owner thread: 0x000001379fbca1e0
[0x00007ffaeb925358] CodeCache_lock - owner thread: 0x000001379fbca1e0
[0x00007ffaeb9254d8] Compile_lock - owner thread: 0x000001379fbca1e0

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000013758000000-0x0000013758ba0000-0x0000013758ba0000), size 12189696, SharedBaseAddress: 0x0000013758000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000013759000000-0x0000013799000000, reserved size: 1073741824
Narrow klass base: 0x0000013758000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 12 total, 12 available
 Memory: 16163M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 10

Heap:
 PSYoungGen      total 20480K, used 9161K [0x00000000d5580000, 0x00000000d6c00000, 0x0000000100000000)
  eden space 18944K, 42% used [0x00000000d5580000,0x00000000d5d56438,0x00000000d6800000)
  from space 1536K, 73% used [0x00000000d6a80000,0x00000000d6b9c010,0x00000000d6c00000)
  to   space 2048K, 0% used [0x00000000d6800000,0x00000000d6800000,0x00000000d6a00000)
 ParOldGen       total 68608K, used 35529K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 51% used [0x0000000080000000,0x00000000822b2680,0x0000000084300000)
 Metaspace       used 42194K, committed 43264K, reserved 1114112K
  class space    used 4208K, committed 4672K, reserved 1048576K

Card table byte_map: [0x00000137404d0000,0x00000137408e0000] _byte_map_base: 0x00000137400d0000

Marking Bits: (ParMarkBitMap*) 0x00007ffaeb92a340
 Begin Bits: [0x0000013752fd0000, 0x0000013754fd0000)
 End Bits:   [0x0000013754fd0000, 0x0000013756fd0000)

Polling page: 0x000001373e780000

Metaspace:

Usage:
  Non-class:     37.10 MB used.
      Class:      4.11 MB used.
       Both:     41.21 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      37.69 MB ( 59%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       4.56 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      42.25 MB (  4%) committed. 

Chunk freelists:
   Non-Class:  9.83 MB
       Class:  11.44 MB
        Both:  21.27 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 58.38 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 968.
num_arena_deaths: 82.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 676.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 115.
num_chunks_taken_from_freelist: 2893.
num_chunk_merges: 30.
num_chunk_splits: 1741.
num_chunks_enlarged: 1014.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=4006Kb max_used=4006Kb free=115993Kb
 bounds [0x000001374b7f0000, 0x000001374bbe0000, 0x0000013752d20000]
CodeHeap 'profiled nmethods': size=120000Kb used=13895Kb max_used=13895Kb free=106104Kb
 bounds [0x0000013743d20000, 0x0000013744ac0000, 0x000001374b250000]
CodeHeap 'non-nmethods': size=5760Kb used=1413Kb max_used=1476Kb free=4346Kb
 bounds [0x000001374b250000, 0x000001374b4c0000, 0x000001374b7f0000]
CodeCache: size=245760Kb, used=19314Kb, max_used=19377Kb, free=226443Kb
 total_blobs=6670, nmethods=5958, adapters=618, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 6.646 Thread 0x000001379fbc86a0 nmethod 6272% 0x000001374bbc9110 code [0x000001374bbc92e0, 0x000001374bbc9818]
Event: 6.646 Thread 0x000001379fbc86a0 6345       4       java.util.HashMap::get (19 bytes)
Event: 6.646 Thread 0x0000013740be9040 nmethod 6353 0x0000013744a9a510 code [0x0000013744a9a840, 0x0000013744a9bf68]
Event: 6.647 Thread 0x0000013740be9040 6354       3       lombok.eclipse.handlers.HandleGetter::fieldQualifiesForGetterGeneration (25 bytes)
Event: 6.647 Thread 0x0000013740be9040 nmethod 6354 0x0000013744a9c790 code [0x0000013744a9c960, 0x0000013744a9cd70]
Event: 6.647 Thread 0x0000013740be9040 6355       3       org.eclipse.jdt.internal.compiler.ast.TypeReference::baseTypeReference (397 bytes)
Event: 6.648 Thread 0x0000013740be9040 nmethod 6355 0x0000013744a9ce90 code [0x0000013744a9d280, 0x0000013744a9e6e0]
Event: 6.648 Thread 0x0000013740be9040 6356       3       org.eclipse.jdt.internal.compiler.SourceElementNotifier::hasDeprecatedAnnotation (52 bytes)
Event: 6.649 Thread 0x0000013740be9040 nmethod 6356 0x0000013744a9ee10 code [0x0000013744a9f000, 0x0000013744a9f3f0]
Event: 6.649 Thread 0x0000013740be9040 6357       3       org.eclipse.jdt.internal.compiler.ast.CompilationUnitDeclaration::getFileName (8 bytes)
Event: 6.649 Thread 0x0000013740be9040 nmethod 6357 0x0000013744a9f610 code [0x0000013744a9f7c0, 0x0000013744a9f900]
Event: 6.649 Thread 0x0000013740be9040 6359       3       org.eclipse.jdt.internal.compiler.lookup.MethodScope::referenceType (18 bytes)
Event: 6.649 Thread 0x0000013740be9040 nmethod 6359 0x0000013744a9fa10 code [0x0000013744a9fbe0, 0x0000013744aa00a8]
Event: 6.649 Thread 0x0000013740be9040 6360       3       org.eclipse.jdt.internal.compiler.lookup.MethodScope::createMethod (483 bytes)
Event: 6.649 Thread 0x000001379fbc86a0 nmethod 6345 0x000001374bbc9b90 code [0x000001374bbc9d40, 0x000001374bbca080]
Event: 6.649 Thread 0x000001379fbc86a0 6358       4       org.eclipse.jdt.internal.compiler.ast.QualifiedTypeReference::getLastToken (13 bytes)
Event: 6.650 Thread 0x000001379fbc86a0 nmethod 6358 0x000001374bbca310 code [0x000001374bbca4a0, 0x000001374bbca568]
Event: 6.650 Thread 0x000001379fbc86a0 6305       4       java.lang.invoke.DirectMethodHandle::checkBase (5 bytes)
Event: 6.650 Thread 0x000001379fbc86a0 nmethod 6305 0x000001374bbca610 code [0x000001374bbca7a0, 0x000001374bbca828]
Event: 6.650 Thread 0x000001379fbc86a0 6309       4       java.util.stream.AbstractPipeline::<init> (91 bytes)

GC Heap History (20 events):
Event: 4.037 GC heap before
{Heap before GC invocations=17 (full 1):
 PSYoungGen      total 24064K, used 23864K [0x00000000d5580000, 0x00000000d6f80000, 0x0000000100000000)
  eden space 21504K, 100% used [0x00000000d5580000,0x00000000d6a80000,0x00000000d6a80000)
  from space 2560K, 92% used [0x00000000d6a80000,0x00000000d6cce210,0x00000000d6d00000)
  to   space 2560K, 0% used [0x00000000d6d00000,0x00000000d6d00000,0x00000000d6f80000)
 ParOldGen       total 68608K, used 27011K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 39% used [0x0000000080000000,0x0000000081a60f48,0x0000000084300000)
 Metaspace       used 33408K, committed 34304K, reserved 1114112K
  class space    used 3415K, committed 3840K, reserved 1048576K
}
Event: 4.040 GC heap after
{Heap after GC invocations=17 (full 1):
 PSYoungGen      total 23552K, used 1792K [0x00000000d5580000, 0x00000000d6f00000, 0x0000000100000000)
  eden space 21504K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a80000)
  from space 2048K, 87% used [0x00000000d6d00000,0x00000000d6ec0240,0x00000000d6f00000)
  to   space 2048K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6d00000)
 ParOldGen       total 68608K, used 29244K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 42% used [0x0000000080000000,0x0000000081c8f158,0x0000000084300000)
 Metaspace       used 33408K, committed 34304K, reserved 1114112K
  class space    used 3415K, committed 3840K, reserved 1048576K
}
Event: 4.322 GC heap before
{Heap before GC invocations=18 (full 1):
 PSYoungGen      total 23552K, used 18580K [0x00000000d5580000, 0x00000000d6f00000, 0x0000000100000000)
  eden space 21504K, 78% used [0x00000000d5580000,0x00000000d65e5190,0x00000000d6a80000)
  from space 2048K, 87% used [0x00000000d6d00000,0x00000000d6ec0240,0x00000000d6f00000)
  to   space 2048K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6d00000)
 ParOldGen       total 68608K, used 29244K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 42% used [0x0000000080000000,0x0000000081c8f158,0x0000000084300000)
 Metaspace       used 34922K, committed 35840K, reserved 1114112K
  class space    used 3570K, committed 4032K, reserved 1048576K
}
Event: 4.325 GC heap after
{Heap after GC invocations=18 (full 1):
 PSYoungGen      total 22528K, used 724K [0x00000000d5580000, 0x00000000d6d80000, 0x0000000100000000)
  eden space 21504K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a80000)
  from space 1024K, 70% used [0x00000000d6b00000,0x00000000d6bb53e8,0x00000000d6c00000)
  to   space 1536K, 0% used [0x00000000d6c00000,0x00000000d6c00000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 30735K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 44% used [0x0000000080000000,0x0000000081e03d20,0x0000000084300000)
 Metaspace       used 34922K, committed 35840K, reserved 1114112K
  class space    used 3570K, committed 4032K, reserved 1048576K
}
Event: 4.325 GC heap before
{Heap before GC invocations=19 (full 2):
 PSYoungGen      total 22528K, used 724K [0x00000000d5580000, 0x00000000d6d80000, 0x0000000100000000)
  eden space 21504K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a80000)
  from space 1024K, 70% used [0x00000000d6b00000,0x00000000d6bb53e8,0x00000000d6c00000)
  to   space 1536K, 0% used [0x00000000d6c00000,0x00000000d6c00000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 30735K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 44% used [0x0000000080000000,0x0000000081e03d20,0x0000000084300000)
 Metaspace       used 34922K, committed 35840K, reserved 1114112K
  class space    used 3570K, committed 4032K, reserved 1048576K
}
Event: 4.361 GC heap after
{Heap after GC invocations=19 (full 2):
 PSYoungGen      total 22528K, used 0K [0x00000000d5580000, 0x00000000d6d80000, 0x0000000100000000)
  eden space 21504K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a80000)
  from space 1024K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6c00000)
  to   space 1536K, 0% used [0x00000000d6c00000,0x00000000d6c00000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 29771K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 43% used [0x0000000080000000,0x0000000081d12f10,0x0000000084300000)
 Metaspace       used 34861K, committed 35840K, reserved 1114112K
  class space    used 3553K, committed 4032K, reserved 1048576K
}
Event: 4.745 GC heap before
{Heap before GC invocations=20 (full 2):
 PSYoungGen      total 22528K, used 21504K [0x00000000d5580000, 0x00000000d6d80000, 0x0000000100000000)
  eden space 21504K, 100% used [0x00000000d5580000,0x00000000d6a80000,0x00000000d6a80000)
  from space 1024K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6c00000)
  to   space 1536K, 0% used [0x00000000d6c00000,0x00000000d6c00000,0x00000000d6d80000)
 ParOldGen       total 68608K, used 29771K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 43% used [0x0000000080000000,0x0000000081d12f10,0x0000000084300000)
 Metaspace       used 36499K, committed 37440K, reserved 1114112K
  class space    used 3705K, committed 4160K, reserved 1048576K
}
Event: 4.747 GC heap after
{Heap after GC invocations=20 (full 2):
 PSYoungGen      total 22528K, used 1530K [0x00000000d5580000, 0x00000000d6e80000, 0x0000000100000000)
  eden space 20992K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a00000)
  from space 1536K, 99% used [0x00000000d6c00000,0x00000000d6d7e878,0x00000000d6d80000)
  to   space 2048K, 0% used [0x00000000d6a00000,0x00000000d6a00000,0x00000000d6c00000)
 ParOldGen       total 68608K, used 29779K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 43% used [0x0000000080000000,0x0000000081d14f10,0x0000000084300000)
 Metaspace       used 36499K, committed 37440K, reserved 1114112K
  class space    used 3705K, committed 4160K, reserved 1048576K
}
Event: 4.818 GC heap before
{Heap before GC invocations=21 (full 2):
 PSYoungGen      total 22528K, used 22398K [0x00000000d5580000, 0x00000000d6e80000, 0x0000000100000000)
  eden space 20992K, 99% used [0x00000000d5580000,0x00000000d69e1160,0x00000000d6a00000)
  from space 1536K, 99% used [0x00000000d6c00000,0x00000000d6d7e878,0x00000000d6d80000)
  to   space 2048K, 0% used [0x00000000d6a00000,0x00000000d6a00000,0x00000000d6c00000)
 ParOldGen       total 68608K, used 29779K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 43% used [0x0000000080000000,0x0000000081d14f10,0x0000000084300000)
 Metaspace       used 36534K, committed 37504K, reserved 1114112K
  class space    used 3705K, committed 4160K, reserved 1048576K
}
Event: 4.819 GC heap after
{Heap after GC invocations=21 (full 2):
 PSYoungGen      total 22016K, used 1014K [0x00000000d5580000, 0x00000000d6d00000, 0x0000000100000000)
  eden space 20992K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a00000)
  from space 1024K, 99% used [0x00000000d6a00000,0x00000000d6afd808,0x00000000d6b00000)
  to   space 1536K, 0% used [0x00000000d6b80000,0x00000000d6b80000,0x00000000d6d00000)
 ParOldGen       total 68608K, used 30703K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 44% used [0x0000000080000000,0x0000000081dfbc38,0x0000000084300000)
 Metaspace       used 36534K, committed 37504K, reserved 1114112K
  class space    used 3705K, committed 4160K, reserved 1048576K
}
Event: 4.989 GC heap before
{Heap before GC invocations=22 (full 2):
 PSYoungGen      total 22016K, used 22006K [0x00000000d5580000, 0x00000000d6d00000, 0x0000000100000000)
  eden space 20992K, 100% used [0x00000000d5580000,0x00000000d6a00000,0x00000000d6a00000)
  from space 1024K, 99% used [0x00000000d6a00000,0x00000000d6afd808,0x00000000d6b00000)
  to   space 1536K, 0% used [0x00000000d6b80000,0x00000000d6b80000,0x00000000d6d00000)
 ParOldGen       total 68608K, used 30703K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 44% used [0x0000000080000000,0x0000000081dfbc38,0x0000000084300000)
 Metaspace       used 38174K, committed 39104K, reserved 1114112K
  class space    used 3869K, committed 4288K, reserved 1048576K
}
Event: 4.991 GC heap after
{Heap after GC invocations=22 (full 2):
 PSYoungGen      total 21504K, used 448K [0x00000000d5580000, 0x00000000d6c00000, 0x0000000100000000)
  eden space 20992K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a00000)
  from space 512K, 87% used [0x00000000d6b80000,0x00000000d6bf0000,0x00000000d6c00000)
  to   space 1024K, 0% used [0x00000000d6a00000,0x00000000d6a00000,0x00000000d6b00000)
 ParOldGen       total 68608K, used 30727K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 44% used [0x0000000080000000,0x0000000081e01c38,0x0000000084300000)
 Metaspace       used 38174K, committed 39104K, reserved 1114112K
  class space    used 3869K, committed 4288K, reserved 1048576K
}
Event: 5.592 GC heap before
{Heap before GC invocations=23 (full 2):
 PSYoungGen      total 21504K, used 21440K [0x00000000d5580000, 0x00000000d6c00000, 0x0000000100000000)
  eden space 20992K, 100% used [0x00000000d5580000,0x00000000d6a00000,0x00000000d6a00000)
  from space 512K, 87% used [0x00000000d6b80000,0x00000000d6bf0000,0x00000000d6c00000)
  to   space 1024K, 0% used [0x00000000d6a00000,0x00000000d6a00000,0x00000000d6b00000)
 ParOldGen       total 68608K, used 30727K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 44% used [0x0000000080000000,0x0000000081e01c38,0x0000000084300000)
 Metaspace       used 39329K, committed 40384K, reserved 1114112K
  class space    used 3982K, committed 4480K, reserved 1048576K
}
Event: 5.593 GC heap after
{Heap after GC invocations=23 (full 2):
 PSYoungGen      total 22016K, used 918K [0x00000000d5580000, 0x00000000d6c00000, 0x0000000100000000)
  eden space 20992K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6a00000)
  from space 1024K, 89% used [0x00000000d6a00000,0x00000000d6ae5b28,0x00000000d6b00000)
  to   space 1024K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6c00000)
 ParOldGen       total 68608K, used 31055K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 45% used [0x0000000080000000,0x0000000081e53c38,0x0000000084300000)
 Metaspace       used 39329K, committed 40384K, reserved 1114112K
  class space    used 3982K, committed 4480K, reserved 1048576K
}
Event: 6.111 GC heap before
{Heap before GC invocations=24 (full 2):
 PSYoungGen      total 22016K, used 21910K [0x00000000d5580000, 0x00000000d6c00000, 0x0000000100000000)
  eden space 20992K, 100% used [0x00000000d5580000,0x00000000d6a00000,0x00000000d6a00000)
  from space 1024K, 89% used [0x00000000d6a00000,0x00000000d6ae5b28,0x00000000d6b00000)
  to   space 1024K, 0% used [0x00000000d6b00000,0x00000000d6b00000,0x00000000d6c00000)
 ParOldGen       total 68608K, used 31055K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 45% used [0x0000000080000000,0x0000000081e53c38,0x0000000084300000)
 Metaspace       used 41261K, committed 42368K, reserved 1114112K
  class space    used 4167K, committed 4672K, reserved 1048576K
}
Event: 6.115 GC heap after
{Heap after GC invocations=24 (full 2):
 PSYoungGen      total 19968K, used 1001K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 18944K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6800000)
  from space 1024K, 97% used [0x00000000d6b00000,0x00000000d6bfa550,0x00000000d6c00000)
  to   space 3072K, 0% used [0x00000000d6800000,0x00000000d6800000,0x00000000d6b00000)
 ParOldGen       total 68608K, used 32684K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 47% used [0x0000000080000000,0x0000000081feb0e0,0x0000000084300000)
 Metaspace       used 41261K, committed 42368K, reserved 1114112K
  class space    used 4167K, committed 4672K, reserved 1048576K
}
Event: 6.397 GC heap before
{Heap before GC invocations=25 (full 2):
 PSYoungGen      total 19968K, used 19945K [0x00000000d5580000, 0x00000000d7000000, 0x0000000100000000)
  eden space 18944K, 100% used [0x00000000d5580000,0x00000000d6800000,0x00000000d6800000)
  from space 1024K, 97% used [0x00000000d6b00000,0x00000000d6bfa550,0x00000000d6c00000)
  to   space 3072K, 0% used [0x00000000d6800000,0x00000000d6800000,0x00000000d6b00000)
 ParOldGen       total 68608K, used 32684K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 47% used [0x0000000080000000,0x0000000081feb0e0,0x0000000084300000)
 Metaspace       used 41753K, committed 42816K, reserved 1114112K
  class space    used 4191K, committed 4672K, reserved 1048576K
}
Event: 6.401 GC heap after
{Heap after GC invocations=25 (full 2):
 PSYoungGen      total 21504K, used 2152K [0x00000000d5580000, 0x00000000d6d00000, 0x0000000100000000)
  eden space 18944K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6800000)
  from space 2560K, 84% used [0x00000000d6800000,0x00000000d6a1a118,0x00000000d6a80000)
  to   space 2560K, 0% used [0x00000000d6a80000,0x00000000d6a80000,0x00000000d6d00000)
 ParOldGen       total 68608K, used 33687K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 49% used [0x0000000080000000,0x00000000820e5cc8,0x0000000084300000)
 Metaspace       used 41753K, committed 42816K, reserved 1114112K
  class space    used 4191K, committed 4672K, reserved 1048576K
}
Event: 6.580 GC heap before
{Heap before GC invocations=26 (full 2):
 PSYoungGen      total 21504K, used 21096K [0x00000000d5580000, 0x00000000d6d00000, 0x0000000100000000)
  eden space 18944K, 99% used [0x00000000d5580000,0x00000000d67ffff8,0x00000000d6800000)
  from space 2560K, 84% used [0x00000000d6800000,0x00000000d6a1a118,0x00000000d6a80000)
  to   space 2560K, 0% used [0x00000000d6a80000,0x00000000d6a80000,0x00000000d6d00000)
 ParOldGen       total 68608K, used 33687K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 49% used [0x0000000080000000,0x00000000820e5cc8,0x0000000084300000)
 Metaspace       used 42109K, committed 43200K, reserved 1114112K
  class space    used 4208K, committed 4672K, reserved 1048576K
}
Event: 6.583 GC heap after
{Heap after GC invocations=26 (full 2):
 PSYoungGen      total 20480K, used 1136K [0x00000000d5580000, 0x00000000d6c00000, 0x0000000100000000)
  eden space 18944K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6800000)
  from space 1536K, 73% used [0x00000000d6a80000,0x00000000d6b9c010,0x00000000d6c00000)
  to   space 2048K, 0% used [0x00000000d6800000,0x00000000d6800000,0x00000000d6a00000)
 ParOldGen       total 68608K, used 35529K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 51% used [0x0000000080000000,0x00000000822b2680,0x0000000084300000)
 Metaspace       used 42109K, committed 43200K, reserved 1114112K
  class space    used 4208K, committed 4672K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.009 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
Event: 0.084 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.102 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
Event: 0.106 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
Event: 0.108 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
Event: 0.112 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.131 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
Event: 0.184 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
Event: 0.928 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
Event: 1.657 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-71916\jna889990872855958034.dll

Deoptimization events (20 events):
Event: 6.499 Thread 0x00000137a07831d0 DEOPT PACKING pc=0x00000137448c240f sp=0x0000000846afc540
Event: 6.499 Thread 0x00000137a07831d0 DEOPT UNPACKING pc=0x000001374b2a78e2 sp=0x0000000846afba58 mode 0
Event: 6.532 Thread 0x000001379fc4c890 DEOPT PACKING pc=0x00000137448c240f sp=0x00000008466fc260
Event: 6.532 Thread 0x000001379fc4c890 DEOPT UNPACKING pc=0x000001374b2a78e2 sp=0x00000008466fb778 mode 0
Event: 6.537 Thread 0x00000137a07831d0 DEOPT PACKING pc=0x00000137448c240f sp=0x0000000846afbb90
Event: 6.537 Thread 0x00000137a07831d0 DEOPT UNPACKING pc=0x000001374b2a78e2 sp=0x0000000846afb0a8 mode 0
Event: 6.540 Thread 0x00000137a07831d0 DEOPT PACKING pc=0x00000137448c240f sp=0x0000000846afbcf0
Event: 6.540 Thread 0x00000137a07831d0 DEOPT UNPACKING pc=0x000001374b2a78e2 sp=0x0000000846afb208 mode 0
Event: 6.561 Thread 0x00000137a07831d0 DEOPT PACKING pc=0x00000137448c240f sp=0x0000000846afc650
Event: 6.561 Thread 0x00000137a07831d0 DEOPT UNPACKING pc=0x000001374b2a78e2 sp=0x0000000846afbb68 mode 0
Event: 6.569 Thread 0x00000137a07831d0 DEOPT PACKING pc=0x00000137448c240f sp=0x0000000846afc410
Event: 6.569 Thread 0x00000137a07831d0 DEOPT UNPACKING pc=0x000001374b2a78e2 sp=0x0000000846afb928 mode 0
Event: 6.569 Thread 0x00000137a07831d0 Uncommon trap: trap_request=0xffffffcc fr.pc=0x000001374bbbce08 relative=0x0000000000001488
Event: 6.569 Thread 0x00000137a07831d0 Uncommon trap: reason=intrinsic_or_type_checked_inlining action=make_not_entrant pc=0x000001374bbbce08 method=java.util.ArrayList.toArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 36 c2
Event: 6.570 Thread 0x00000137a07831d0 DEOPT PACKING pc=0x000001374bbbce08 sp=0x0000000846afc470
Event: 6.570 Thread 0x00000137a07831d0 DEOPT UNPACKING pc=0x000001374b2a6da2 sp=0x0000000846afc3b0 mode 2
Event: 6.570 Thread 0x00000137a07831d0 Uncommon trap: trap_request=0xffffffcc fr.pc=0x000001374bbad874 relative=0x0000000000000ff4
Event: 6.570 Thread 0x00000137a07831d0 Uncommon trap: reason=intrinsic_or_type_checked_inlining action=make_not_entrant pc=0x000001374bbad874 method=java.util.ArrayList.toArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 36 c2
Event: 6.570 Thread 0x00000137a07831d0 DEOPT PACKING pc=0x000001374bbad874 sp=0x0000000846afc260
Event: 6.570 Thread 0x00000137a07831d0 DEOPT UNPACKING pc=0x000001374b2a6da2 sp=0x0000000846afc1a0 mode 2

Classes loaded (20 events):
Event: 4.981 Loading class java/util/Hashtable$ValueCollection
Event: 4.981 Loading class java/util/Hashtable$ValueCollection done
Event: 5.117 Loading class java/util/WeakHashMap$KeyIterator
Event: 5.117 Loading class java/util/WeakHashMap$KeyIterator done
Event: 5.120 Loading class java/util/concurrent/locks/ReentrantReadWriteLock$Sync$HoldCounter
Event: 5.120 Loading class java/util/concurrent/locks/ReentrantReadWriteLock$Sync$HoldCounter done
Event: 5.134 Loading class java/util/regex/PatternSyntaxException
Event: 5.134 Loading class java/util/regex/PatternSyntaxException done
Event: 5.207 Loading class sun/reflect/misc/ReflectUtil
Event: 5.207 Loading class sun/reflect/misc/ReflectUtil done
Event: 5.611 Loading class java/io/FileReader
Event: 5.611 Loading class java/io/FileReader done
Event: 5.611 Loading class java/lang/UnsupportedClassVersionError
Event: 5.611 Loading class java/lang/UnsupportedClassVersionError done
Event: 5.763 Loading class java/util/stream/ReduceOps$4
Event: 5.763 Loading class java/util/stream/ReduceOps$4 done
Event: 5.763 Loading class java/util/stream/ReduceOps$4ReducingSink
Event: 5.763 Loading class java/util/stream/ReduceOps$4ReducingSink done
Event: 6.041 Loading class java/util/regex/Pattern$Pos
Event: 6.041 Loading class java/util/regex/Pattern$Pos done

Classes unloaded (20 events):
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x0000013759382800 'java/lang/invoke/LambdaForm$MH+0x0000013759382800'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x0000013759382400 'java/lang/invoke/LambdaForm$MH+0x0000013759382400'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x000001375936e400 'java/lang/invoke/LambdaForm$MH+0x000001375936e400'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x000001375936e000 'java/lang/invoke/LambdaForm$MH+0x000001375936e000'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x000001375936d800 'java/lang/invoke/LambdaForm$MH+0x000001375936d800'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x000001375936d400 'java/lang/invoke/LambdaForm$MH+0x000001375936d400'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x000001375936cc00 'java/lang/invoke/LambdaForm$MH+0x000001375936cc00'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x000001375936c800 'java/lang/invoke/LambdaForm$MH+0x000001375936c800'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x000001375936c000 'java/lang/invoke/LambdaForm$MH+0x000001375936c000'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x000001375936bc00 'java/lang/invoke/LambdaForm$MH+0x000001375936bc00'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x000001375936b400 'java/lang/invoke/LambdaForm$MH+0x000001375936b400'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x000001375936b000 'java/lang/invoke/LambdaForm$MH+0x000001375936b000'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x000001375936a400 'java/lang/invoke/LambdaForm$MH+0x000001375936a400'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x000001375936a800 'java/lang/invoke/LambdaForm$MH+0x000001375936a800'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x0000013759369c00 'java/lang/invoke/LambdaForm$MH+0x0000013759369c00'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x0000013759369800 'java/lang/invoke/LambdaForm$MH+0x0000013759369800'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x0000013759369000 'java/lang/invoke/LambdaForm$MH+0x0000013759369000'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x0000013759369400 'java/lang/invoke/LambdaForm$MH+0x0000013759369400'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x0000013759368400 'java/lang/invoke/LambdaForm$DMH+0x0000013759368400'
Event: 4.333 Thread 0x0000013740bce770 Unloading class 0x0000013759368000 'java/lang/invoke/LambdaForm$DMH+0x0000013759368000'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 3.529 Thread 0x00000137a0783860 Implicit null exception at 0x000001374b9c5f76 to 0x000001374b9c62ac
Event: 3.530 Thread 0x00000137a0783860 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6842de0}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d6842de0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.530 Thread 0x000001379fc4dc40 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d67e8508}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d67e8508) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.590 Thread 0x000001379fc4dc40 Implicit null exception at 0x000001374b8b529d to 0x000001374b8b5d94
Event: 4.994 Thread 0x00000137a07831d0 Implicit null exception at 0x000001374b943386 to 0x000001374b94347c
Event: 4.994 Thread 0x00000137a0783860 Implicit null exception at 0x000001374b943386 to 0x000001374b94347c
Event: 5.015 Thread 0x00000137a0783860 Exception <a 'java/lang/LinkageError'{0x00000000d58168c0}: loader lombok.launch.ShadowClassLoader @2ea9c284 attempted duplicate class definition for lombok.launch.PatchFixesHider$FieldInitializer. (lombok.launch.PatchFixesHider$FieldInitializer is in unnamed module of loader lombok.launch.ShadowClassLoader @2ea9c284, parent loader org.eclipse.osgi.internal.loader.EquinoxClassLoader @1c9ad3f3)> (0x00000000d58168c0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 1682]
Event: 5.104 Thread 0x00000137a07831d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d588e610}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d588e610) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 5.105 Thread 0x00000137a0783860 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5aaa4b0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d5aaa4b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 5.225 Thread 0x000001379fc4dc40 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d6891688}: Found class java.lang.Object, but interface was expected> (0x00000000d6891688) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 5.229 Thread 0x000001379fc4dc40 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d68a0d80}: Found class java.lang.Object, but interface was expected> (0x00000000d68a0d80) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 5.583 Thread 0x000001379fc4e2d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6929208}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, long)'> (0x00000000d6929208) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 5.606 Thread 0x00000137a0783860 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'{0x00000000d56cfdd8}> (0x00000000d56cfdd8) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 625]
Event: 5.606 Thread 0x00000137a0781100 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'{0x00000000d56c4d20}> (0x00000000d56c4d20) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 625]
Event: 5.645 Thread 0x00000137a07831d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d57886f0}: 'int java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object)'> (0x00000000d57886f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 5.691 Thread 0x00000137a07831d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5985350}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x00000000d5985350) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 5.691 Thread 0x00000137a07831d0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d5988610}: Found class java.lang.Object, but interface was expected> (0x00000000d5988610) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 5.768 Thread 0x00000137a07831d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5c8a528}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5c8a528) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 5.781 Thread 0x00000137a07831d0 Exception <a 'java/io/FileNotFoundException'{0x00000000d5cf7ca8}> (0x00000000d5cf7ca8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 5.781 Thread 0x00000137a07831d0 Exception <a 'java/io/FileNotFoundException'{0x00000000d5cf8ca0}> (0x00000000d5cf8ca0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 5.617 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.617 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.788 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.788 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 6.004 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 6.004 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 6.018 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 6.018 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 6.053 Executing VM operation: ICBufferFull
Event: 6.054 Executing VM operation: ICBufferFull done
Event: 6.111 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 6.115 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 6.157 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 6.158 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 6.348 Executing VM operation: ICBufferFull
Event: 6.349 Executing VM operation: ICBufferFull done
Event: 6.396 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 6.401 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 6.580 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 6.583 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x00000137440dd910
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x000001374411ab10
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x000001374411af90
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x000001374411f890
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x000001374414f990
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x0000013744150390
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x0000013744154c90
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x0000013744173810
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x0000013744182a90
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x0000013744185390
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x000001374418d910
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x00000137441b6010
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x00000137441ba210
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x00000137441bb610
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x00000137441fd990
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x00000137441fe210
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x00000137441fe610
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x000001374420d010
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x0000013744211b10
Event: 4.341 Thread 0x0000013740bce770 flushing  nmethod 0x0000013744217110

Events (20 events):
Event: 2.656 Thread 0x0000013740b34bf0 Thread added: 0x000001379fc51750
Event: 2.656 Thread 0x0000013740b34bf0 Thread added: 0x000001379fc4a7c0
Event: 2.656 Thread 0x0000013740b34bf0 Thread added: 0x000001379fc4ae50
Event: 2.657 Thread 0x0000013740b34bf0 Thread added: 0x000001379fc503a0
Event: 2.658 Thread 0x0000013740b34bf0 Thread added: 0x000001379fc51de0
Event: 2.658 Thread 0x0000013740b34bf0 Thread added: 0x000001379fc4bb70
Event: 2.658 Thread 0x0000013740b34bf0 Thread added: 0x000001379fc510c0
Event: 2.658 Thread 0x0000013740b34bf0 Thread added: 0x000001379fc4b4e0
Event: 2.658 Thread 0x0000013740b34bf0 Thread added: 0x000001379fc4c200
Event: 2.692 Thread 0x0000013740b34bf0 Thread added: 0x000001379fc4eff0
Event: 2.898 Thread 0x000001379fc50a30 Thread added: 0x000001379fc4c890
Event: 2.911 Thread 0x0000013740b34bf0 Thread added: 0x000001379fc4d5b0
Event: 2.911 Thread 0x0000013740b34bf0 Thread added: 0x000001379fc4dc40
Event: 3.175 Thread 0x000001379fc4dc40 Thread added: 0x00000137a0783860
Event: 3.179 Thread 0x000001379fc4dc40 Thread added: 0x00000137a07831d0
Event: 4.265 Thread 0x000001379fbc8d70 Thread exited: 0x000001379fbc8d70
Event: 4.793 Thread 0x000001379fbca1e0 Thread added: 0x000001379fbcb650
Event: 5.241 Thread 0x000001379fc4dc40 Thread added: 0x00000137a0781100
Event: 5.582 Thread 0x000001379fbcb650 Thread exited: 0x000001379fbcb650
Event: 5.604 Thread 0x0000013740be9040 Thread added: 0x000001379fbc86a0


Dynamic libraries:
0x00007ff7195b0000 - 0x00007ff7195be000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.exe
0x00007ffb67ed0000 - 0x00007ffb680e7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb66ef0000 - 0x00007ffb66fb4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb656e0000 - 0x00007ffb65ab0000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb651d0000 - 0x00007ffb652e1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffb485e0000 - 0x00007ffb485fe000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffb48380000 - 0x00007ffb48398000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jli.dll
0x00007ffb66c10000 - 0x00007ffb66dc1000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb4af60000 - 0x00007ffb4b1fc000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe\COMCTL32.dll
0x00007ffb65b30000 - 0x00007ffb65b56000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb67c50000 - 0x00007ffb67cf7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb67e60000 - 0x00007ffb67e89000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb65070000 - 0x00007ffb65193000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb64fd0000 - 0x00007ffb6506a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb65b60000 - 0x00007ffb65b91000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffb4a670000 - 0x00007ffb4a67c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffb48010000 - 0x00007ffb4809d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\msvcp140.dll
0x00007ffaeac70000 - 0x00007ffaeba07000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server\jvm.dll
0x00007ffb66b50000 - 0x00007ffb66c01000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb66fc0000 - 0x00007ffb67068000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb651a0000 - 0x00007ffb651c8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffb66dd0000 - 0x00007ffb66ee8000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb67a30000 - 0x00007ffb67aa1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb63db0000 - 0x00007ffb63dfd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffb62690000 - 0x00007ffb6269a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb5af50000 - 0x00007ffb5af84000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb63d90000 - 0x00007ffb63da3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffb63ff0000 - 0x00007ffb64008000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffb4a4f0000 - 0x00007ffb4a4fa000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
0x00007ffb61d80000 - 0x00007ffb61fb3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb66690000 - 0x00007ffb66a21000 	C:\WINDOWS\System32\combase.dll
0x00007ffb67070000 - 0x00007ffb67148000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffb4f720000 - 0x00007ffb4f752000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb653b0000 - 0x00007ffb6542b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffb4a160000 - 0x00007ffb4a16f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
0x00007ffb48340000 - 0x00007ffb4835f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
0x00007ffb65ba0000 - 0x00007ffb66441000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffb65430000 - 0x00007ffb6556f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffb62e60000 - 0x00007ffb6377a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffb67400000 - 0x00007ffb6750a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffb66ae0000 - 0x00007ffb66b49000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffb64f00000 - 0x00007ffb64f25000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffb48320000 - 0x00007ffb48338000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
0x00007ffb4a0f0000 - 0x00007ffb4a100000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
0x00007ffb5e820000 - 0x00007ffb5e94c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffb64530000 - 0x00007ffb64599000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffb48240000 - 0x00007ffb48256000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
0x00007ffb495b0000 - 0x00007ffb495c0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
0x00007ffb49230000 - 0x00007ffb49274000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
0x00007ffb67ab0000 - 0x00007ffb67c50000 	C:\WINDOWS\System32\ole32.dll
0x00007ffb647c0000 - 0x00007ffb647db000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffb63fb0000 - 0x00007ffb63fe7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffb64640000 - 0x00007ffb64668000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffb647e0000 - 0x00007ffb647ec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffb63a50000 - 0x00007ffb63a7d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffb67160000 - 0x00007ffb67169000 	C:\WINDOWS\System32\NSI.dll
0x00007ffb1a5c0000 - 0x00007ffb1a609000 	C:\Users\<USER>\AppData\Local\Temp\jna-71916\jna889990872855958034.dll
0x00007ffb673e0000 - 0x00007ffb673e8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffb5e9f0000 - 0x00007ffb5ea09000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffb5e600000 - 0x00007ffb5e61f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL

JVMTI agents:
c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar path:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll, loaded, initialized, instrumentlib options:none

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854;C:\Users\<USER>\AppData\Local\Temp\jna-71916

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java\ss_ws --pipe=\\.\pipe\lsp-41b511739beb21790d8d2cdf6ddf0c09-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytesseract;C:\Program Files\Common Files\Oracle\Java\javapath;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\TortoiseSVN\bin;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Cloudflare\Cloudflare WARP\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Docker\Docker\resources\bin;D:\A\Git\Git\cmd;C:\Program Files\CMake\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;D:\A\Anaconda;D:\A\Anaconda\Library\mingw-w64\bin;D:\A\Anaconda\Library\usr\bin;D:\A\Anaconda\Library\bin;D:\A\Anaconda\Scripts;D:\A\Scripts\;D:\A\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin
USERNAME=HUY
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 0 days 5:26 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x3c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for the first 12 processors :
  Max Mhz: 2688, Current Mhz: 2688, Mhz Limit: 2688

Memory: 4k page, system-wide physical 16163M (1850M free)
TotalPageFile size 20259M (AvailPageFile size 9M)
current process WorkingSet (physical memory assigned to process): 234M, peak: 243M
current process commit charge ("private bytes"): 344M, peak: 359M

vm_info: OpenJDK 64-Bit Server VM (21.0.8+9-LTS) for windows-amd64 JRE (21.0.8+9-LTS), built on 2025-07-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
