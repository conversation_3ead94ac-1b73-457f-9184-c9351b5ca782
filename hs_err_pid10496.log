#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1057072 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=10496, tid=20064
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.8+9 (21.0.8+9) (build 21.0.8+9-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.8+9 (21.0.8+9-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-65555c3215d7753d30058106555404a8-sock

Host: 11th Gen Intel(R) Core(TM) i5-11400H @ 2.70GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Thu Sep 25 03:08:41 2025 SE Asia Standard Time elapsed time: 11.130816 seconds (0d 0h 0m 11s)

---------------  T H R E A D  ---------------

Current thread (0x000002e556fd30e0):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=20064, stack(0x000000f8bdb00000,0x000000f8bdc00000) (1024K)]


Current CompileTask:
C2:11130 10035 % !   4       org.eclipse.jdt.internal.core.JarPackageFragmentRoot::computeChildren @ 246 (605 bytes)

Stack: [0x000000f8bdb00000,0x000000f8bdc00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d2449]
V  [jvm.dll+0x8ae341]
V  [jvm.dll+0x8b08be]
V  [jvm.dll+0x8b0fa3]
V  [jvm.dll+0x280c96]
V  [jvm.dll+0xc581d]
V  [jvm.dll+0xc5d53]
V  [jvm.dll+0x3b919c]
V  [jvm.dll+0x1e1213]
V  [jvm.dll+0x249022]
V  [jvm.dll+0x2484af]
V  [jvm.dll+0x1c89ee]
V  [jvm.dll+0x257d4d]
V  [jvm.dll+0x2562ea]
V  [jvm.dll+0x3f2d16]
V  [jvm.dll+0x857e6b]
V  [jvm.dll+0x6d0b0d]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002e558652c00, length=56, elements={
0x000002e576eb32a0, 0x000002e57e94c8b0, 0x000002e57e94e1b0, 0x000002e576f564a0,
0x000002e576f573b0, 0x000002e576f59c20, 0x000002e576f5efb0, 0x000002e576f658a0,
0x000002e576f62460, 0x000002e5500f1040, 0x000002e5500f1d60, 0x000002e5500f16d0,
0x000002e5500f3110, 0x000002e5500f23f0, 0x000002e5500f2a80, 0x000002e5500f37a0,
0x000002e5500f3e30, 0x000002e555dd4af0, 0x000002e555dd4460, 0x000002e555dd1d00,
0x000002e555dd2390, 0x000002e556fd30e0, 0x000002e555dd0fe0, 0x000002e555dd0950,
0x000002e555dd2a20, 0x000002e555dd5180, 0x000002e555dd5810, 0x000002e555dd1670,
0x000002e555dd5ea0, 0x000002e555dcfc30, 0x000002e555dd30b0, 0x000002e555dd3740,
0x000002e555dd3dd0, 0x000002e555dd6530, 0x000002e555dcf5a0, 0x000002e555dd02c0,
0x000002e555dd6bc0, 0x000002e55971edc0, 0x000002e55971c660, 0x000002e55971da10,
0x000002e559720800, 0x000002e55971e0a0, 0x000002e55971bfd0, 0x000002e55971fae0,
0x000002e559720e90, 0x000002e55971d380, 0x000002e55971e730, 0x000002e55971f450,
0x000002e559720170, 0x000002e559719f00, 0x000002e559721520, 0x000002e55971a590,
0x000002e55971ccf0, 0x000002e55971ac20, 0x000002e55523dff0, 0x000002e5575a28f0
}

Java Threads: ( => current thread )
  0x000002e576eb32a0 JavaThread "main"                              [_thread_blocked, id=22184, stack(0x000000f8bce00000,0x000000f8bcf00000) (1024K)]
  0x000002e57e94c8b0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=15184, stack(0x000000f8bd200000,0x000000f8bd300000) (1024K)]
  0x000002e57e94e1b0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=7552, stack(0x000000f8bd300000,0x000000f8bd400000) (1024K)]
  0x000002e576f564a0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=7844, stack(0x000000f8bd400000,0x000000f8bd500000) (1024K)]
  0x000002e576f573b0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=21180, stack(0x000000f8bd500000,0x000000f8bd600000) (1024K)]
  0x000002e576f59c20 JavaThread "Service Thread"             daemon [_thread_blocked, id=8720, stack(0x000000f8bd600000,0x000000f8bd700000) (1024K)]
  0x000002e576f5efb0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=7820, stack(0x000000f8bd700000,0x000000f8bd800000) (1024K)]
  0x000002e576f658a0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=18072, stack(0x000000f8bd800000,0x000000f8bd900000) (1024K)]
  0x000002e576f62460 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=11572, stack(0x000000f8bd900000,0x000000f8bda00000) (1024K)]
  0x000002e5500f1040 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=19940, stack(0x000000f8bda00000,0x000000f8bdb00000) (1024K)]
  0x000002e5500f1d60 JavaThread "Notification Thread"        daemon [_thread_blocked, id=2808, stack(0x000000f8bdc00000,0x000000f8bdd00000) (1024K)]
  0x000002e5500f16d0 JavaThread "Active Thread: Equinox Container: f7e94dcc-19a2-415a-b520-6a7a42ba8e85"        [_thread_blocked, id=23312, stack(0x000000f8be400000,0x000000f8be500000) (1024K)]
  0x000002e5500f3110 JavaThread "Refresh Thread: Equinox Container: f7e94dcc-19a2-415a-b520-6a7a42ba8e85" daemon [_thread_blocked, id=2724, stack(0x000000f8be500000,0x000000f8be600000) (1024K)]
  0x000002e5500f23f0 JavaThread "Framework Event Dispatcher: Equinox Container: f7e94dcc-19a2-415a-b520-6a7a42ba8e85" daemon [_thread_blocked, id=15636, stack(0x000000f8be700000,0x000000f8be800000) (1024K)]
  0x000002e5500f2a80 JavaThread "Start Level: Equinox Container: f7e94dcc-19a2-415a-b520-6a7a42ba8e85" daemon [_thread_blocked, id=9568, stack(0x000000f8be800000,0x000000f8be900000) (1024K)]
  0x000002e5500f37a0 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=14924, stack(0x000000f8beb00000,0x000000f8bec00000) (1024K)]
  0x000002e5500f3e30 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=6472, stack(0x000000f8bec00000,0x000000f8bed00000) (1024K)]
  0x000002e555dd4af0 JavaThread "Worker-JM"                         [_thread_blocked, id=14996, stack(0x000000f8bee00000,0x000000f8bef00000) (1024K)]
  0x000002e555dd4460 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=19160, stack(0x000000f8bef00000,0x000000f8bf000000) (1024K)]
  0x000002e555dd1d00 JavaThread "Worker-0: Publish Diagnostics"        [_thread_blocked, id=16308, stack(0x000000f8bf000000,0x000000f8bf100000) (1024K)]
  0x000002e555dd2390 JavaThread "Worker-1: Initialize workspace"        [_thread_blocked, id=20436, stack(0x000000f8bf100000,0x000000f8bf200000) (1024K)]
=>0x000002e556fd30e0 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=20064, stack(0x000000f8bdb00000,0x000000f8bdc00000) (1024K)]
  0x000002e555dd0fe0 JavaThread "Java indexing"              daemon [_thread_blocked, id=20676, stack(0x000000f8bdd00000,0x000000f8bde00000) (1024K)]
  0x000002e555dd0950 JavaThread "Worker-2"                          [_thread_blocked, id=14316, stack(0x000000f8bf300000,0x000000f8bf400000) (1024K)]
  0x000002e555dd2a20 JavaThread "Worker-3"                          [_thread_blocked, id=8584, stack(0x000000f8bf400000,0x000000f8bf500000) (1024K)]
  0x000002e555dd5180 JavaThread "Worker-4"                          [_thread_blocked, id=19512, stack(0x000000f8bf500000,0x000000f8bf600000) (1024K)]
  0x000002e555dd5810 JavaThread "Worker-5"                          [_thread_blocked, id=20944, stack(0x000000f8bf600000,0x000000f8bf700000) (1024K)]
  0x000002e555dd1670 JavaThread "Thread-2"                   daemon [_thread_in_native, id=7324, stack(0x000000f8bf700000,0x000000f8bf800000) (1024K)]
  0x000002e555dd5ea0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=16128, stack(0x000000f8bf800000,0x000000f8bf900000) (1024K)]
  0x000002e555dcfc30 JavaThread "Thread-4"                   daemon [_thread_in_native, id=21644, stack(0x000000f8bf900000,0x000000f8bfa00000) (1024K)]
  0x000002e555dd30b0 JavaThread "Thread-5"                   daemon [_thread_in_native, id=13272, stack(0x000000f8bfa00000,0x000000f8bfb00000) (1024K)]
  0x000002e555dd3740 JavaThread "Thread-6"                   daemon [_thread_in_native, id=18804, stack(0x000000f8bfb00000,0x000000f8bfc00000) (1024K)]
  0x000002e555dd3dd0 JavaThread "Thread-7"                   daemon [_thread_in_native, id=1436, stack(0x000000f8bfc00000,0x000000f8bfd00000) (1024K)]
  0x000002e555dd6530 JavaThread "Thread-8"                   daemon [_thread_in_native, id=15108, stack(0x000000f8bfd00000,0x000000f8bfe00000) (1024K)]
  0x000002e555dcf5a0 JavaThread "Thread-9"                   daemon [_thread_in_native, id=17840, stack(0x000000f8bfe00000,0x000000f8bff00000) (1024K)]
  0x000002e555dd02c0 JavaThread "Thread-10"                  daemon [_thread_in_native, id=20876, stack(0x000000f8bff00000,0x000000f8c0000000) (1024K)]
  0x000002e555dd6bc0 JavaThread "Thread-11"                  daemon [_thread_in_native, id=22324, stack(0x000000f8c0000000,0x000000f8c0100000) (1024K)]
  0x000002e55971edc0 JavaThread "Thread-12"                  daemon [_thread_in_native, id=18992, stack(0x000000f8c0100000,0x000000f8c0200000) (1024K)]
  0x000002e55971c660 JavaThread "Thread-13"                  daemon [_thread_in_native, id=10024, stack(0x000000f8c0200000,0x000000f8c0300000) (1024K)]
  0x000002e55971da10 JavaThread "Thread-14"                  daemon [_thread_in_native, id=1356, stack(0x000000f8c0300000,0x000000f8c0400000) (1024K)]
  0x000002e559720800 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=19432, stack(0x000000f8c0400000,0x000000f8c0500000) (1024K)]
  0x000002e55971e0a0 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=2092, stack(0x000000f8c0500000,0x000000f8c0600000) (1024K)]
  0x000002e55971bfd0 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=22276, stack(0x000000f8c0600000,0x000000f8c0700000) (1024K)]
  0x000002e55971fae0 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=22852, stack(0x000000f8c0700000,0x000000f8c0800000) (1024K)]
  0x000002e559720e90 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=19380, stack(0x000000f8c0800000,0x000000f8c0900000) (1024K)]
  0x000002e55971d380 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=14776, stack(0x000000f8c0900000,0x000000f8c0a00000) (1024K)]
  0x000002e55971e730 JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_blocked, id=4144, stack(0x000000f8c0a00000,0x000000f8c0b00000) (1024K)]
  0x000002e55971f450 JavaThread "ForkJoinPool.commonPool-worker-5" daemon [_thread_blocked, id=21848, stack(0x000000f8c0b00000,0x000000f8c0c00000) (1024K)]
  0x000002e559720170 JavaThread "ForkJoinPool.commonPool-worker-6" daemon [_thread_blocked, id=16428, stack(0x000000f8c0c00000,0x000000f8c0d00000) (1024K)]
  0x000002e559719f00 JavaThread "ForkJoinPool.commonPool-worker-7" daemon [_thread_blocked, id=21964, stack(0x000000f8c0d00000,0x000000f8c0e00000) (1024K)]
  0x000002e559721520 JavaThread "ForkJoinPool.commonPool-worker-8" daemon [_thread_blocked, id=3728, stack(0x000000f8c0e00000,0x000000f8c0f00000) (1024K)]
  0x000002e55971a590 JavaThread "ForkJoinPool.commonPool-worker-9" daemon [_thread_blocked, id=8760, stack(0x000000f8c0f00000,0x000000f8c1000000) (1024K)]
  0x000002e55971ccf0 JavaThread "ForkJoinPool.commonPool-worker-10" daemon [_thread_blocked, id=4368, stack(0x000000f8c1000000,0x000000f8c1100000) (1024K)]
  0x000002e55971ac20 JavaThread "ForkJoinPool.commonPool-worker-11" daemon [_thread_blocked, id=8036, stack(0x000000f8c1100000,0x000000f8c1200000) (1024K)]
  0x000002e55523dff0 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=23512, stack(0x000000f8bed00000,0x000000f8bee00000) (1024K)]
  0x000002e5575a28f0 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=16352, stack(0x000000f8c1200000,0x000000f8c1300000) (1024K)]
Total: 56

Other Threads:
  0x000002e574b40170 VMThread "VM Thread"                           [id=1564, stack(0x000000f8bd100000,0x000000f8bd200000) (1024K)]
  0x000002e576f20ab0 WatcherThread "VM Periodic Task Thread"        [id=18212, stack(0x000000f8bd000000,0x000000f8bd100000) (1024K)]
  0x000002e574b3fdd0 WorkerThread "GC Thread#0"                     [id=22736, stack(0x000000f8bcf00000,0x000000f8bd000000) (1024K)]
  0x000002e574b3f690 WorkerThread "GC Thread#1"                     [id=15392, stack(0x000000f8bde00000,0x000000f8bdf00000) (1024K)]
  0x000002e574b3fa30 WorkerThread "GC Thread#2"                     [id=16280, stack(0x000000f8bdf00000,0x000000f8be000000) (1024K)]
  0x000002e555483490 WorkerThread "GC Thread#3"                     [id=10584, stack(0x000000f8be000000,0x000000f8be100000) (1024K)]
  0x000002e5554858d0 WorkerThread "GC Thread#4"                     [id=21976, stack(0x000000f8be100000,0x000000f8be200000) (1024K)]
  0x000002e5554846b0 WorkerThread "GC Thread#5"                     [id=15844, stack(0x000000f8be200000,0x000000f8be300000) (1024K)]
  0x000002e555484df0 WorkerThread "GC Thread#6"                     [id=7132, stack(0x000000f8be300000,0x000000f8be400000) (1024K)]
  0x000002e555482d50 WorkerThread "GC Thread#7"                     [id=16720, stack(0x000000f8be600000,0x000000f8be700000) (1024K)]
  0x000002e5554829b0 WorkerThread "GC Thread#8"                     [id=3980, stack(0x000000f8be900000,0x000000f8bea00000) (1024K)]
  0x000002e5554830f0 WorkerThread "GC Thread#9"                     [id=20132, stack(0x000000f8bea00000,0x000000f8beb00000) (1024K)]
Total: 12

Threads with active compile tasks:
C2 CompilerThread0  11146 10120       4       java.lang.invoke.MethodType::makeImpl (109 bytes)
C2 CompilerThread1  11155 10035 % !   4       org.eclipse.jdt.internal.core.JarPackageFragmentRoot::computeChildren @ 246 (605 bytes)
C2 CompilerThread2  11155 10045 %     4       java.util.Properties::load0 @ 29 (249 bytes)
Total: 3

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffaeb925458] Threads_lock - owner thread: 0x000002e574b40170
[0x00007ffaeb925558] Heap_lock - owner thread: 0x000002e559720e90

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002e50f000000-0x000002e50fba0000-0x000002e50fba0000), size 12189696, SharedBaseAddress: 0x000002e50f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002e510000000-0x000002e550000000, reserved size: 1073741824
Narrow klass base: 0x000002e50f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 12 total, 12 available
 Memory: 16163M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 10

Heap:
 PSYoungGen      total 12288K, used 12160K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 512K, 75% used [0x00000000d6200000,0x00000000d6260000,0x00000000d6280000)
  to   space 512K, 62% used [0x00000000d6280000,0x00000000d62d0000,0x00000000d6300000)
 ParOldGen       total 151040K, used 150562K [0x0000000080000000, 0x0000000089380000, 0x00000000d5580000)
  object space 151040K, 99% used [0x0000000080000000,0x0000000089308bf0,0x0000000089380000)
 Metaspace       used 61048K, committed 62528K, reserved 1114112K
  class space    used 6456K, committed 7104K, reserved 1048576K

Card table byte_map: [0x000002e576870000,0x000002e576c80000] _byte_map_base: 0x000002e576470000

Marking Bits: (ParMarkBitMap*) 0x00007ffaeb92a340
 Begin Bits: [0x000002e57a350000, 0x000002e57c350000)
 End Bits:   [0x000002e57c350000, 0x000002e57e350000)

Polling page: 0x000002e574da0000

Metaspace:

Usage:
  Non-class:     53.31 MB used.
      Class:      6.31 MB used.
       Both:     59.62 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      54.12 MB ( 85%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       6.94 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      61.06 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  9.05 MB
       Class:  9.06 MB
        Both:  18.11 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 97.31 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 11.
num_arena_births: 1132.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 977.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 25.
num_chunks_taken_from_freelist: 3963.
num_chunk_merges: 12.
num_chunk_splits: 2460.
num_chunks_enlarged: 1414.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=6276Kb max_used=6276Kb free=113723Kb
 bounds [0x000002e507ad0000, 0x000002e508100000, 0x000002e50f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=20588Kb max_used=20588Kb free=99411Kb
 bounds [0x000002e500000000, 0x000002e501420000, 0x000002e507530000]
CodeHeap 'non-nmethods': size=5760Kb used=1428Kb max_used=1494Kb free=4331Kb
 bounds [0x000002e507530000, 0x000002e5077a0000, 0x000002e507ad0000]
CodeCache: size=245760Kb, used=28292Kb, max_used=28358Kb, free=217465Kb
 total_blobs=10174, nmethods=9445, adapters=635, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 11.099 Thread 0x000002e576f62460 10212       3       org.eclipse.jdt.internal.core.nd.java.model.BinaryModuleFactory::createDescriptor (9 bytes)
Event: 11.100 Thread 0x000002e576f62460 nmethod 10212 0x000002e501413410 code [0x000002e501413600, 0x000002e501413d50]
Event: 11.100 Thread 0x000002e576f62460 10213       3       org.eclipse.jdt.internal.core.util.Util::concatWith (90 bytes)
Event: 11.100 Thread 0x000002e576f62460 nmethod 10213 0x000002e501413f90 code [0x000002e501414200, 0x000002e501414a78]
Event: 11.101 Thread 0x000002e576f62460 10214       3       org.eclipse.sisu.plexus.RealmFilteredBeans$FilteredItr::next (5 bytes)
Event: 11.101 Thread 0x000002e576f62460 nmethod 10214 0x000002e501414e90 code [0x000002e501415060, 0x000002e501415338]
Event: 11.101 Thread 0x000002e576f62460 10215       3       org.eclipse.sisu.plexus.RealmFilteredBeans$FilteredItr::next (27 bytes)
Event: 11.101 Thread 0x000002e576f62460 nmethod 10215 0x000002e501415490 code [0x000002e501415660, 0x000002e5014158e0]
Event: 11.113 Thread 0x000002e576f62460 10216       3       org.eclipse.sisu.inject.Implementations$ClassFinder::visit (6 bytes)
Event: 11.114 Thread 0x000002e576f62460 nmethod 10216 0x000002e501415a10 code [0x000002e501415bc0, 0x000002e501415e48]
Event: 11.118 Thread 0x000002e576f62460 10217       3       com.google.inject.Scopes::isSingleton (108 bytes)
Event: 11.118 Thread 0x000002e576f62460 nmethod 10217 0x000002e501415f90 code [0x000002e501416240, 0x000002e501417378]
Event: 11.119 Thread 0x000002e576f62460 10219       3       org.eclipse.sisu.plexus.RealmManager::contextRealm (33 bytes)
Event: 11.119 Thread 0x000002e576f62460 nmethod 10219 0x000002e501417810 code [0x000002e501417a00, 0x000002e501418030]
Event: 11.119 Thread 0x000002e576f62460 10218       3       org.eclipse.sisu.plexus.RealmFilteredBeans::iterator (45 bytes)
Event: 11.119 Thread 0x000002e576f62460 nmethod 10218 0x000002e501418210 code [0x000002e501418460, 0x000002e501418e70]
Event: 11.119 Thread 0x000002e576f62460 10220       3       org.eclipse.sisu.plexus.RealmManager::visibleRealmNames (51 bytes)
Event: 11.120 Thread 0x000002e576f62460 nmethod 10220 0x000002e501419190 code [0x000002e501419380, 0x000002e501419840]
Event: 11.121 Thread 0x000002e576f62460 10221       3       java.lang.reflect.Method::getDefaultValue (110 bytes)
Event: 11.121 Thread 0x000002e576f62460 nmethod 10221 0x000002e501419990 code [0x000002e501419cc0, 0x000002e50141aba8]

GC Heap History (20 events):
Event: 11.008 GC heap before
{Heap before GC invocations=183 (full 3):
 PSYoungGen      total 12288K, used 12128K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 512K, 68% used [0x00000000d6200000,0x00000000d6258020,0x00000000d6280000)
  to   space 512K, 0% used [0x00000000d6280000,0x00000000d6280000,0x00000000d6300000)
 ParOldGen       total 149504K, used 149082K [0x0000000080000000, 0x0000000089200000, 0x00000000d5580000)
  object space 149504K, 99% used [0x0000000080000000,0x0000000089196bd0,0x0000000089200000)
 Metaspace       used 60980K, committed 62464K, reserved 1114112K
  class space    used 6453K, committed 7104K, reserved 1048576K
}
Event: 11.010 GC heap after
{Heap after GC invocations=183 (full 3):
 PSYoungGen      total 12288K, used 256K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 512K, 50% used [0x00000000d6280000,0x00000000d62c0000,0x00000000d6300000)
  to   space 512K, 0% used [0x00000000d6200000,0x00000000d6200000,0x00000000d6280000)
 ParOldGen       total 149504K, used 149250K [0x0000000080000000, 0x0000000089200000, 0x00000000d5580000)
  object space 149504K, 99% used [0x0000000080000000,0x00000000891c0bf0,0x0000000089200000)
 Metaspace       used 60980K, committed 62464K, reserved 1114112K
  class space    used 6453K, committed 7104K, reserved 1048576K
}
Event: 11.019 GC heap before
{Heap before GC invocations=184 (full 3):
 PSYoungGen      total 12288K, used 12032K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 512K, 50% used [0x00000000d6280000,0x00000000d62c0000,0x00000000d6300000)
  to   space 512K, 0% used [0x00000000d6200000,0x00000000d6200000,0x00000000d6280000)
 ParOldGen       total 149504K, used 149250K [0x0000000080000000, 0x0000000089200000, 0x00000000d5580000)
  object space 149504K, 99% used [0x0000000080000000,0x00000000891c0bf0,0x0000000089200000)
 Metaspace       used 60984K, committed 62464K, reserved 1114112K
  class space    used 6453K, committed 7104K, reserved 1048576K
}
Event: 11.021 GC heap after
{Heap after GC invocations=184 (full 3):
 PSYoungGen      total 12288K, used 384K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 512K, 75% used [0x00000000d6200000,0x00000000d6260000,0x00000000d6280000)
  to   space 512K, 0% used [0x00000000d6280000,0x00000000d6280000,0x00000000d6300000)
 ParOldGen       total 149504K, used 149362K [0x0000000080000000, 0x0000000089200000, 0x00000000d5580000)
  object space 149504K, 99% used [0x0000000080000000,0x00000000891dcbf0,0x0000000089200000)
 Metaspace       used 60984K, committed 62464K, reserved 1114112K
  class space    used 6453K, committed 7104K, reserved 1048576K
}
Event: 11.034 GC heap before
{Heap before GC invocations=185 (full 3):
 PSYoungGen      total 12288K, used 12160K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 512K, 75% used [0x00000000d6200000,0x00000000d6260000,0x00000000d6280000)
  to   space 512K, 0% used [0x00000000d6280000,0x00000000d6280000,0x00000000d6300000)
 ParOldGen       total 149504K, used 149362K [0x0000000080000000, 0x0000000089200000, 0x00000000d5580000)
  object space 149504K, 99% used [0x0000000080000000,0x00000000891dcbf0,0x0000000089200000)
 Metaspace       used 60987K, committed 62464K, reserved 1114112K
  class space    used 6453K, committed 7104K, reserved 1048576K
}
Event: 11.035 GC heap after
{Heap after GC invocations=185 (full 3):
 PSYoungGen      total 12288K, used 320K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 512K, 62% used [0x00000000d6280000,0x00000000d62d0000,0x00000000d6300000)
  to   space 512K, 0% used [0x00000000d6200000,0x00000000d6200000,0x00000000d6280000)
 ParOldGen       total 150016K, used 149522K [0x0000000080000000, 0x0000000089280000, 0x00000000d5580000)
  object space 150016K, 99% used [0x0000000080000000,0x0000000089204bf0,0x0000000089280000)
 Metaspace       used 60987K, committed 62464K, reserved 1114112K
  class space    used 6453K, committed 7104K, reserved 1048576K
}
Event: 11.046 GC heap before
{Heap before GC invocations=186 (full 3):
 PSYoungGen      total 12288K, used 12096K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 512K, 62% used [0x00000000d6280000,0x00000000d62d0000,0x00000000d6300000)
  to   space 512K, 0% used [0x00000000d6200000,0x00000000d6200000,0x00000000d6280000)
 ParOldGen       total 150016K, used 149522K [0x0000000080000000, 0x0000000089280000, 0x00000000d5580000)
  object space 150016K, 99% used [0x0000000080000000,0x0000000089204bf0,0x0000000089280000)
 Metaspace       used 60999K, committed 62464K, reserved 1114112K
  class space    used 6455K, committed 7104K, reserved 1048576K
}
Event: 11.047 GC heap after
{Heap after GC invocations=186 (full 3):
 PSYoungGen      total 12288K, used 256K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 512K, 50% used [0x00000000d6200000,0x00000000d6240000,0x00000000d6280000)
  to   space 512K, 0% used [0x00000000d6280000,0x00000000d6280000,0x00000000d6300000)
 ParOldGen       total 150016K, used 149650K [0x0000000080000000, 0x0000000089280000, 0x00000000d5580000)
  object space 150016K, 99% used [0x0000000080000000,0x0000000089224bf0,0x0000000089280000)
 Metaspace       used 60999K, committed 62464K, reserved 1114112K
  class space    used 6455K, committed 7104K, reserved 1048576K
}
Event: 11.058 GC heap before
{Heap before GC invocations=187 (full 3):
 PSYoungGen      total 12288K, used 12032K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 512K, 50% used [0x00000000d6200000,0x00000000d6240000,0x00000000d6280000)
  to   space 512K, 0% used [0x00000000d6280000,0x00000000d6280000,0x00000000d6300000)
 ParOldGen       total 150016K, used 149650K [0x0000000080000000, 0x0000000089280000, 0x00000000d5580000)
  object space 150016K, 99% used [0x0000000080000000,0x0000000089224bf0,0x0000000089280000)
 Metaspace       used 61010K, committed 62464K, reserved 1114112K
  class space    used 6456K, committed 7104K, reserved 1048576K
}
Event: 11.059 GC heap after
{Heap after GC invocations=187 (full 3):
 PSYoungGen      total 12288K, used 224K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 512K, 43% used [0x00000000d6280000,0x00000000d62b8000,0x00000000d6300000)
  to   space 512K, 0% used [0x00000000d6200000,0x00000000d6200000,0x00000000d6280000)
 ParOldGen       total 150016K, used 149786K [0x0000000080000000, 0x0000000089280000, 0x00000000d5580000)
  object space 150016K, 99% used [0x0000000080000000,0x0000000089246bf0,0x0000000089280000)
 Metaspace       used 61010K, committed 62464K, reserved 1114112K
  class space    used 6456K, committed 7104K, reserved 1048576K
}
Event: 11.073 GC heap before
{Heap before GC invocations=188 (full 3):
 PSYoungGen      total 12288K, used 12000K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 512K, 43% used [0x00000000d6280000,0x00000000d62b8000,0x00000000d6300000)
  to   space 512K, 0% used [0x00000000d6200000,0x00000000d6200000,0x00000000d6280000)
 ParOldGen       total 150016K, used 149786K [0x0000000080000000, 0x0000000089280000, 0x00000000d5580000)
  object space 150016K, 99% used [0x0000000080000000,0x0000000089246bf0,0x0000000089280000)
 Metaspace       used 61017K, committed 62464K, reserved 1114112K
  class space    used 6456K, committed 7104K, reserved 1048576K
}
Event: 11.074 GC heap after
{Heap after GC invocations=188 (full 3):
 PSYoungGen      total 12288K, used 352K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 512K, 68% used [0x00000000d6200000,0x00000000d6258000,0x00000000d6280000)
  to   space 512K, 0% used [0x00000000d6280000,0x00000000d6280000,0x00000000d6300000)
 ParOldGen       total 150016K, used 149850K [0x0000000080000000, 0x0000000089280000, 0x00000000d5580000)
  object space 150016K, 99% used [0x0000000080000000,0x0000000089256bf0,0x0000000089280000)
 Metaspace       used 61017K, committed 62464K, reserved 1114112K
  class space    used 6456K, committed 7104K, reserved 1048576K
}
Event: 11.087 GC heap before
{Heap before GC invocations=189 (full 3):
 PSYoungGen      total 12288K, used 12128K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 512K, 68% used [0x00000000d6200000,0x00000000d6258000,0x00000000d6280000)
  to   space 512K, 0% used [0x00000000d6280000,0x00000000d6280000,0x00000000d6300000)
 ParOldGen       total 150016K, used 149850K [0x0000000080000000, 0x0000000089280000, 0x00000000d5580000)
  object space 150016K, 99% used [0x0000000080000000,0x0000000089256bf0,0x0000000089280000)
 Metaspace       used 61023K, committed 62464K, reserved 1114112K
  class space    used 6456K, committed 7104K, reserved 1048576K
}
Event: 11.088 GC heap after
{Heap after GC invocations=189 (full 3):
 PSYoungGen      total 12288K, used 288K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 512K, 56% used [0x00000000d6280000,0x00000000d62c8000,0x00000000d6300000)
  to   space 512K, 0% used [0x00000000d6200000,0x00000000d6200000,0x00000000d6280000)
 ParOldGen       total 150016K, used 150010K [0x0000000080000000, 0x0000000089280000, 0x00000000d5580000)
  object space 150016K, 99% used [0x0000000080000000,0x000000008927ebf0,0x0000000089280000)
 Metaspace       used 61023K, committed 62464K, reserved 1114112K
  class space    used 6456K, committed 7104K, reserved 1048576K
}
Event: 11.101 GC heap before
{Heap before GC invocations=190 (full 3):
 PSYoungGen      total 12288K, used 12064K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 512K, 56% used [0x00000000d6280000,0x00000000d62c8000,0x00000000d6300000)
  to   space 512K, 0% used [0x00000000d6200000,0x00000000d6200000,0x00000000d6280000)
 ParOldGen       total 150016K, used 150010K [0x0000000080000000, 0x0000000089280000, 0x00000000d5580000)
  object space 150016K, 99% used [0x0000000080000000,0x000000008927ebf0,0x0000000089280000)
 Metaspace       used 61042K, committed 62528K, reserved 1114112K
  class space    used 6456K, committed 7104K, reserved 1048576K
}
Event: 11.102 GC heap after
{Heap after GC invocations=190 (full 3):
 PSYoungGen      total 12288K, used 288K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 512K, 56% used [0x00000000d6200000,0x00000000d6248000,0x00000000d6280000)
  to   space 512K, 0% used [0x00000000d6280000,0x00000000d6280000,0x00000000d6300000)
 ParOldGen       total 150528K, used 150090K [0x0000000080000000, 0x0000000089300000, 0x00000000d5580000)
  object space 150528K, 99% used [0x0000000080000000,0x0000000089292bf0,0x0000000089300000)
 Metaspace       used 61042K, committed 62528K, reserved 1114112K
  class space    used 6456K, committed 7104K, reserved 1048576K
}
Event: 11.111 GC heap before
{Heap before GC invocations=191 (full 3):
 PSYoungGen      total 12288K, used 12064K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 512K, 56% used [0x00000000d6200000,0x00000000d6248000,0x00000000d6280000)
  to   space 512K, 0% used [0x00000000d6280000,0x00000000d6280000,0x00000000d6300000)
 ParOldGen       total 150528K, used 150090K [0x0000000080000000, 0x0000000089300000, 0x00000000d5580000)
  object space 150528K, 99% used [0x0000000080000000,0x0000000089292bf0,0x0000000089300000)
 Metaspace       used 61042K, committed 62528K, reserved 1114112K
  class space    used 6456K, committed 7104K, reserved 1048576K
}
Event: 11.113 GC heap after
{Heap after GC invocations=191 (full 3):
 PSYoungGen      total 12288K, used 320K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 512K, 62% used [0x00000000d6280000,0x00000000d62d0000,0x00000000d6300000)
  to   space 512K, 0% used [0x00000000d6200000,0x00000000d6200000,0x00000000d6280000)
 ParOldGen       total 150528K, used 150210K [0x0000000080000000, 0x0000000089300000, 0x00000000d5580000)
  object space 150528K, 99% used [0x0000000080000000,0x00000000892b0bf0,0x0000000089300000)
 Metaspace       used 61042K, committed 62528K, reserved 1114112K
  class space    used 6456K, committed 7104K, reserved 1048576K
}
Event: 11.122 GC heap before
{Heap before GC invocations=192 (full 3):
 PSYoungGen      total 12288K, used 12096K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 512K, 62% used [0x00000000d6280000,0x00000000d62d0000,0x00000000d6300000)
  to   space 512K, 0% used [0x00000000d6200000,0x00000000d6200000,0x00000000d6280000)
 ParOldGen       total 150528K, used 150210K [0x0000000080000000, 0x0000000089300000, 0x00000000d5580000)
  object space 150528K, 99% used [0x0000000080000000,0x00000000892b0bf0,0x0000000089300000)
 Metaspace       used 61048K, committed 62528K, reserved 1114112K
  class space    used 6456K, committed 7104K, reserved 1048576K
}
Event: 11.123 GC heap after
{Heap after GC invocations=192 (full 3):
 PSYoungGen      total 12288K, used 384K [0x00000000d5580000, 0x00000000d6300000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 512K, 75% used [0x00000000d6200000,0x00000000d6260000,0x00000000d6280000)
  to   space 512K, 0% used [0x00000000d6280000,0x00000000d6280000,0x00000000d6300000)
 ParOldGen       total 150528K, used 150442K [0x0000000080000000, 0x0000000089300000, 0x00000000d5580000)
  object space 150528K, 99% used [0x0000000080000000,0x00000000892eabf0,0x0000000089300000)
 Metaspace       used 61048K, committed 62528K, reserved 1114112K
  class space    used 6456K, committed 7104K, reserved 1048576K
}

Dll operation events (11 events):
Event: 0.009 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
Event: 0.067 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.082 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
Event: 0.087 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
Event: 0.089 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
Event: 0.092 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.105 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
Event: 0.167 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
Event: 0.937 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
Event: 2.270 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-71916\jna6744573782807631077.dll
Event: 8.747 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 10.975 Thread 0x000002e555dd2390 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000002e5080dda28 relative=0x0000000000000188
Event: 10.975 Thread 0x000002e555dd2390 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000002e5080dda28 method=java.util.HashMap.keysToArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 36 c2
Event: 10.975 Thread 0x000002e555dd2390 DEOPT PACKING pc=0x000002e5080dda28 sp=0x000000f8bf1fdda0
Event: 10.975 Thread 0x000002e555dd2390 DEOPT UNPACKING pc=0x000002e507586da2 sp=0x000000f8bf1fdd20 mode 2
Event: 11.000 Thread 0x000002e555dd2390 DEOPT PACKING pc=0x000002e50074ca1c sp=0x000000f8bf1fdf30
Event: 11.000 Thread 0x000002e555dd2390 DEOPT UNPACKING pc=0x000002e5075878e2 sp=0x000000f8bf1fd3a0 mode 0
Event: 11.003 Thread 0x000002e555dd2390 DEOPT PACKING pc=0x000002e50074c9fb sp=0x000000f8bf1fdf30
Event: 11.003 Thread 0x000002e555dd2390 DEOPT UNPACKING pc=0x000002e5075878e2 sp=0x000000f8bf1fd3a0 mode 0
Event: 11.012 Thread 0x000002e555dd2390 DEOPT PACKING pc=0x000002e50074c9fb sp=0x000000f8bf1fdf30
Event: 11.012 Thread 0x000002e555dd2390 DEOPT UNPACKING pc=0x000002e5075878e2 sp=0x000000f8bf1fd3a0 mode 0
Event: 11.015 Thread 0x000002e555dd2390 DEOPT PACKING pc=0x000002e50074c9fb sp=0x000000f8bf1fdf30
Event: 11.015 Thread 0x000002e555dd2390 DEOPT UNPACKING pc=0x000002e5075878e2 sp=0x000000f8bf1fd3a0 mode 0
Event: 11.015 Thread 0x000002e555dd2390 DEOPT PACKING pc=0x000002e50074c9fb sp=0x000000f8bf1fdf30
Event: 11.015 Thread 0x000002e555dd2390 DEOPT UNPACKING pc=0x000002e5075878e2 sp=0x000000f8bf1fd3a0 mode 0
Event: 11.030 Thread 0x000002e555dd2390 DEOPT PACKING pc=0x000002e50074c9fb sp=0x000000f8bf1fdf30
Event: 11.030 Thread 0x000002e555dd2390 DEOPT UNPACKING pc=0x000002e5075878e2 sp=0x000000f8bf1fd3a0 mode 0
Event: 11.033 Thread 0x000002e555dd2390 DEOPT PACKING pc=0x000002e50074c9fb sp=0x000000f8bf1fdf30
Event: 11.033 Thread 0x000002e555dd2390 DEOPT UNPACKING pc=0x000002e5075878e2 sp=0x000000f8bf1fd3a0 mode 0
Event: 11.037 Thread 0x000002e555dd2390 DEOPT PACKING pc=0x000002e50074c9fb sp=0x000000f8bf1fdf30
Event: 11.037 Thread 0x000002e555dd2390 DEOPT UNPACKING pc=0x000002e5075878e2 sp=0x000000f8bf1fd3a0 mode 0

Classes loaded (20 events):
Event: 8.785 Loading class com/sun/crypto/provider/PKCS5Padding
Event: 8.785 Loading class com/sun/crypto/provider/Padding
Event: 8.785 Loading class com/sun/crypto/provider/Padding done
Event: 8.785 Loading class com/sun/crypto/provider/PKCS5Padding done
Event: 8.785 Loading class com/sun/crypto/provider/CipherBlockChaining
Event: 8.785 Loading class com/sun/crypto/provider/CipherBlockChaining done
Event: 8.786 Loading class javax/crypto/NullCipher
Event: 8.786 Loading class javax/crypto/NullCipher done
Event: 8.786 Loading class sun/security/util/ArrayUtil
Event: 8.786 Loading class sun/security/util/ArrayUtil done
Event: 8.816 Loading class java/util/zip/CheckedInputStream
Event: 8.816 Loading class java/util/zip/CheckedInputStream done
Event: 8.869 Loading class java/util/zip/GZIPInputStream$1
Event: 8.869 Loading class java/util/zip/GZIPInputStream$1 done
Event: 9.171 Loading class java/lang/UnsupportedClassVersionError
Event: 9.171 Loading class java/lang/UnsupportedClassVersionError done
Event: 10.404 Loading class sun/nio/cs/ISO_8859_1$Decoder
Event: 10.404 Loading class sun/nio/cs/ISO_8859_1$Decoder done
Event: 11.084 Loading class java/lang/invoke/MethodHandleImpl$CountingWrapper$1
Event: 11.085 Loading class java/lang/invoke/MethodHandleImpl$CountingWrapper$1 done

Classes unloaded (7 events):
Event: 2.959 Thread 0x000002e574b40170 Unloading class 0x000002e5101ac800 'java/lang/invoke/LambdaForm$MH+0x000002e5101ac800'
Event: 2.959 Thread 0x000002e574b40170 Unloading class 0x000002e5101ac400 'java/lang/invoke/LambdaForm$MH+0x000002e5101ac400'
Event: 2.959 Thread 0x000002e574b40170 Unloading class 0x000002e5101ac000 'java/lang/invoke/LambdaForm$MH+0x000002e5101ac000'
Event: 2.959 Thread 0x000002e574b40170 Unloading class 0x000002e5101abc00 'java/lang/invoke/LambdaForm$MH+0x000002e5101abc00'
Event: 2.959 Thread 0x000002e574b40170 Unloading class 0x000002e5101ab800 'java/lang/invoke/LambdaForm$BMH+0x000002e5101ab800'
Event: 2.959 Thread 0x000002e574b40170 Unloading class 0x000002e5101ab400 'java/lang/invoke/LambdaForm$DMH+0x000002e5101ab400'
Event: 2.959 Thread 0x000002e574b40170 Unloading class 0x000002e5101aa000 'java/lang/invoke/LambdaForm$DMH+0x000002e5101aa000'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 8.726 Thread 0x000002e555dd2390 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5ad7af0}> (0x00000000d5ad7af0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 8.741 Thread 0x000002e559720170 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5bffa40}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5bffa40) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.771 Thread 0x000002e5500f44c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5df0350}> (0x00000000d5df0350) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 8.904 Thread 0x000002e555dd2390 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65ce698}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d65ce698) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.922 Thread 0x000002e555dd2390 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d558b928}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, int)'> (0x00000000d558b928) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 9.045 Thread 0x000002e555dd1d00 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5e4f620}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, long)'> (0x00000000d5e4f620) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 9.154 Thread 0x000002e55971bfd0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d56965f0}: Found class java.lang.Object, but interface was expected> (0x00000000d56965f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 9.172 Thread 0x000002e559719f00 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'{0x00000000d5f98ac8}> (0x00000000d5f98ac8) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 625]
Event: 9.195 Thread 0x000002e559720e90 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d56a2f68}: 'int java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object)'> (0x00000000d56a2f68) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 9.421 Thread 0x000002e559720e90 Implicit null exception at 0x000002e507e94d06 to 0x000002e507e94e31
Event: 9.462 Thread 0x000002e559720e90 Implicit null exception at 0x000002e507e7e4a6 to 0x000002e507e7ea74
Event: 9.786 Thread 0x000002e555dd1d00 Implicit null exception at 0x000002e507fecd70 to 0x000002e507ff23b0
Event: 10.088 Thread 0x000002e555dd2390 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5c90938}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x00000000d5c90938) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 10.112 Thread 0x000002e5575a2260 Implicit null exception at 0x000002e507c82b82 to 0x000002e507c82c7c
Event: 10.112 Thread 0x000002e5575a2260 Implicit null exception at 0x000002e507cbcbda to 0x000002e507cbcc61
Event: 10.112 Thread 0x000002e55759cd10 Implicit null exception at 0x000002e507cbcbda to 0x000002e507cbcc61
Event: 10.115 Thread 0x000002e55759cd10 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d558dd68}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d558dd68) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 10.303 Thread 0x000002e559720e90 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d57b62f8}: Found class java.lang.Object, but interface was expected> (0x00000000d57b62f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 10.926 Thread 0x000002e555dd2390 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5717748}> (0x00000000d5717748) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 10.926 Thread 0x000002e555dd2390 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d58309d8}> (0x00000000d58309d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 11.008 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 11.010 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 11.019 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 11.021 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 11.034 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 11.035 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 11.046 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 11.047 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 11.058 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 11.059 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 11.073 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 11.074 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 11.087 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 11.088 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 11.101 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 11.102 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 11.111 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 11.113 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 11.121 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 11.123 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 9.110 Thread 0x000002e574b40170 flushing osr nmethod 0x000002e50085e510
Event: 9.110 Thread 0x000002e574b40170 flushing osr nmethod 0x000002e50089b410
Event: 9.110 Thread 0x000002e574b40170 flushing osr nmethod 0x000002e5008a8b90
Event: 9.110 Thread 0x000002e574b40170 flushing osr nmethod 0x000002e5008b0890
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e5008bb390
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e5008c4390
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e5008cd290
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e5008d0790
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e5008e6290
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e5008e7510
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e5008e7b10
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e5008e8390
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e5008e8a90
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e5008ea890
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e5008f3610
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e5008f9210
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e50097c390
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e50097de90
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e50098b990
Event: 9.110 Thread 0x000002e574b40170 flushing  nmethod 0x000002e50098d510

Events (20 events):
Event: 5.171 Thread 0x000002e559721520 Thread added: 0x000002e55971ccf0
Event: 5.176 Thread 0x000002e55971a590 Thread added: 0x000002e55971ac20
Event: 6.454 Thread 0x000002e555f50430 Thread exited: 0x000002e555f50430
Event: 6.786 Thread 0x000002e576f62460 Thread added: 0x000002e55523fb30
Event: 8.107 Thread 0x000002e5500f44c0 Thread exited: 0x000002e5500f44c0
Event: 8.221 Thread 0x000002e55523fb30 Thread exited: 0x000002e55523fb30
Event: 8.252 Thread 0x000002e576f62460 Thread added: 0x000002e55523dff0
Event: 8.719 Thread 0x000002e55971fae0 Thread added: 0x000002e5500f44c0
Event: 8.738 Thread 0x000002e555dd2390 Thread added: 0x000002e5575a28f0
Event: 9.168 Thread 0x000002e5500f44c0 Thread exited: 0x000002e5500f44c0
Event: 10.107 Thread 0x000002e555dd2390 Thread added: 0x000002e55759cd10
Event: 10.107 Thread 0x000002e555dd2390 Thread added: 0x000002e55759bff0
Event: 10.107 Thread 0x000002e555dd2390 Thread added: 0x000002e5575a1540
Event: 10.107 Thread 0x000002e555dd2390 Thread added: 0x000002e5575a1bd0
Event: 10.107 Thread 0x000002e555dd2390 Thread added: 0x000002e5575a2260
Event: 10.636 Thread 0x000002e5575a1540 Thread exited: 0x000002e5575a1540
Event: 10.636 Thread 0x000002e5575a1bd0 Thread exited: 0x000002e5575a1bd0
Event: 10.636 Thread 0x000002e5575a2260 Thread exited: 0x000002e5575a2260
Event: 10.636 Thread 0x000002e55759cd10 Thread exited: 0x000002e55759cd10
Event: 10.636 Thread 0x000002e55759bff0 Thread exited: 0x000002e55759bff0


Dynamic libraries:
0x00007ff7195b0000 - 0x00007ff7195be000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.exe
0x00007ffb67ed0000 - 0x00007ffb680e7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb66ef0000 - 0x00007ffb66fb4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb656e0000 - 0x00007ffb65ab0000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb651d0000 - 0x00007ffb652e1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffb48380000 - 0x00007ffb48398000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jli.dll
0x00007ffb485e0000 - 0x00007ffb485fe000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffb66c10000 - 0x00007ffb66dc1000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb4af60000 - 0x00007ffb4b1fc000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe\COMCTL32.dll
0x00007ffb65b30000 - 0x00007ffb65b56000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb67c50000 - 0x00007ffb67cf7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb67e60000 - 0x00007ffb67e89000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb65070000 - 0x00007ffb65193000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb64fd0000 - 0x00007ffb6506a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb65b60000 - 0x00007ffb65b91000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffb4a670000 - 0x00007ffb4a67c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffb48010000 - 0x00007ffb4809d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\msvcp140.dll
0x00007ffaeac70000 - 0x00007ffaeba07000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server\jvm.dll
0x00007ffb66b50000 - 0x00007ffb66c01000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb66fc0000 - 0x00007ffb67068000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb651a0000 - 0x00007ffb651c8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffb66dd0000 - 0x00007ffb66ee8000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb67a30000 - 0x00007ffb67aa1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb63db0000 - 0x00007ffb63dfd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffb62690000 - 0x00007ffb6269a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb5af50000 - 0x00007ffb5af84000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb63d90000 - 0x00007ffb63da3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffb63ff0000 - 0x00007ffb64008000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffb4a4f0000 - 0x00007ffb4a4fa000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
0x00007ffb61d80000 - 0x00007ffb61fb3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb66690000 - 0x00007ffb66a21000 	C:\WINDOWS\System32\combase.dll
0x00007ffb67070000 - 0x00007ffb67148000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffb4f720000 - 0x00007ffb4f752000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb653b0000 - 0x00007ffb6542b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffb4a160000 - 0x00007ffb4a16f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
0x00007ffb48340000 - 0x00007ffb4835f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
0x00007ffb65ba0000 - 0x00007ffb66441000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffb65430000 - 0x00007ffb6556f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffb62e60000 - 0x00007ffb6377a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffb67400000 - 0x00007ffb6750a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffb66ae0000 - 0x00007ffb66b49000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffb64f00000 - 0x00007ffb64f25000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffb48320000 - 0x00007ffb48338000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
0x00007ffb4a0f0000 - 0x00007ffb4a100000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
0x00007ffb5e820000 - 0x00007ffb5e94c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffb64530000 - 0x00007ffb64599000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffb48240000 - 0x00007ffb48256000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
0x00007ffb495b0000 - 0x00007ffb495c0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
0x00007ffb483e0000 - 0x00007ffb48424000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
0x00007ffb67ab0000 - 0x00007ffb67c50000 	C:\WINDOWS\System32\ole32.dll
0x00007ffb647c0000 - 0x00007ffb647db000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffb63fb0000 - 0x00007ffb63fe7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffb64640000 - 0x00007ffb64668000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffb647e0000 - 0x00007ffb647ec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffb63a50000 - 0x00007ffb63a7d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffb67160000 - 0x00007ffb67169000 	C:\WINDOWS\System32\NSI.dll
0x00007ffb13020000 - 0x00007ffb13069000 	C:\Users\<USER>\AppData\Local\Temp\jna-71916\jna6744573782807631077.dll
0x00007ffb673e0000 - 0x00007ffb673e8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffb5e9f0000 - 0x00007ffb5ea09000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffb5e600000 - 0x00007ffb5e61f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffb528b0000 - 0x00007ffb528be000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\sunmscapi.dll
0x00007ffb65570000 - 0x00007ffb656d8000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffb64960000 - 0x00007ffb6498d000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffb64920000 - 0x00007ffb64957000 	C:\WINDOWS\SYSTEM32\NTASN1.dll

JVMTI agents:
c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar path:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll, loaded, initialized, instrumentlib options:none

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854;C:\Users\<USER>\AppData\Local\Temp\jna-71916

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-65555c3215d7753d30058106555404a8-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\411cee776fb741e5038a86c9371be00a\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytesseract;C:\Program Files\Common Files\Oracle\Java\javapath;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\TortoiseSVN\bin;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Cloudflare\Cloudflare WARP\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Docker\Docker\resources\bin;D:\A\Git\Git\cmd;C:\Program Files\CMake\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;D:\A\Anaconda;D:\A\Anaconda\Library\mingw-w64\bin;D:\A\Anaconda\Library\usr\bin;D:\A\Anaconda\Library\bin;D:\A\Anaconda\Scripts;D:\A\Scripts\;D:\A\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin
USERNAME=HUY
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 0 days 5:26 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x3c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for the first 12 processors :
  Max Mhz: 2688, Current Mhz: 2688, Mhz Limit: 2688

Memory: 4k page, system-wide physical 16163M (1924M free)
TotalPageFile size 20259M (AvailPageFile size 8M)
current process WorkingSet (physical memory assigned to process): 393M, peak: 397M
current process commit charge ("private bytes"): 471M, peak: 1693M

vm_info: OpenJDK 64-Bit Server VM (21.0.8+9-LTS) for windows-amd64 JRE (21.0.8+9-LTS), built on 2025-07-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
